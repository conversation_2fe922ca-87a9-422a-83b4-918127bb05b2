FROM node:18.20.2-alpine AS node 

WORKDIR /tmp/mybizv2/

COPY ./ /tmp/mybizv2/

ARG BASE_VERSION
ARG BASE_ENV
ENV VUE_APP_BASE_VERSION $BASE_VERSION
ENV VUE_APP_BASE_ENV $BASE_ENV

RUN npm install -g pnpm

# 安装所有依赖
RUN pnpm install

# 然后构建内部依赖
RUN npm run buildDependencies

# 最后构建主应用
RUN pnpm vite build

FROM nginx 

COPY --from=node /tmp/mybizv2/dist/ /var/www/vue/mybizv2

RUN ["rm", "-rf", "/etc/nginx/nginx.conf"]
RUN ["rm", "-rf", "/etc/nginx/conf.d/*"]
COPY ./nginx/nginx.conf /etc/nginx/nginx.conf 
RUN ["chmod", "777", "/etc/nginx/nginx.conf"]

EXPOSE 8886

ENTRYPOINT ["nginx", "-c", "/etc/nginx/nginx.conf", "-g", "daemon off;"]
