# 环境变量配置说明

## 📋 概述

项目使用 Vite 的环境变量系统来管理不同环境下的配置。环境变量以 `VITE_` 开头，可以在客户端代码中使用。

## 📁 环境文件

- `.env` - 所有环境的通用配置
- `.env.development` - 开发环境专用配置
- `.env.production` - 生产环境专用配置

## 🔧 可用环境变量

### 应用配置

| 变量名             | 类型   | 默认值     | 说明       |
| ------------------ | ------ | ---------- | ---------- |
| `VITE_APP_TITLE`   | string | MyBusiness | 应用标题   |
| `VITE_APP_VERSION` | string | 1.0.0      | 应用版本号 |

### API 配置

| 变量名              | 类型   | 默认值                | 说明                 |
| ------------------- | ------ | --------------------- | -------------------- |
| `VITE_API_BASE_URL` | string | /mybusiness           | API 基础路径         |
| `VITE_API_TARGET`   | string | http://localhost:8080 | 代理目标地址         |
| `VITE_API_TIMEOUT`  | number | 10000                 | 请求超时时间（毫秒） |
| `VITE_AUTH_URL`     | string | /mybusiness/auth      | 权限验证地址         |

### 调试配置

| 变量名                    | 类型    | 默认值     | 说明                 |
| ------------------------- | ------- | ---------- | -------------------- |
| `VITE_ENABLE_CONSOLE_LOG` | boolean | true/false | 是否启用控制台日志   |
| `VITE_SHOW_DEBUG_INFO`    | boolean | true/false | 是否显示详细调试信息 |
| `VITE_ENABLE_MOCK`        | boolean | false      | 是否启用模拟数据     |

### 上传配置

| 变量名                 | 类型   | 默认值             | 说明                     |
| ---------------------- | ------ | ------------------ | ------------------------ |
| `VITE_UPLOAD_MAX_SIZE` | number | 10485760           | 文件上传大小限制（字节） |
| `VITE_UPLOAD_URL`      | string | /mybusiness/upload | 文件上传地址             |

### 安全配置

| 变量名              | 类型    | 默认值     | 说明           |
| ------------------- | ------- | ---------- | -------------- |
| `VITE_ENABLE_HTTPS` | boolean | false/true | 是否启用 HTTPS |

## 🌍 环境说明

### 开发环境 (.env.development)

```bash
# 开发环境配置
NODE_ENV=development

# API 配置
VITE_API_BASE_URL=/mybusiness
VITE_API_TARGET=http://localhost:8080
VITE_API_TIMEOUT=10000

# 调试配置
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_MOCK=false
VITE_SHOW_DEBUG_INFO=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_URL=/mybusiness/upload

# 权限配置
VITE_AUTH_URL=/mybusiness/auth
```

### 生产环境 (.env.production)

```bash
# 生产环境配置
NODE_ENV=production

# API 配置
VITE_API_BASE_URL=/mybusiness
VITE_API_TARGET=https://api.mybusiness.com
VITE_API_TIMEOUT=15000

# 调试配置
VITE_ENABLE_CONSOLE_LOG=false
VITE_ENABLE_MOCK=false
VITE_SHOW_DEBUG_INFO=false

# 上传配置
VITE_UPLOAD_MAX_SIZE=20971520
VITE_UPLOAD_URL=/mybusiness/upload

# 权限配置
VITE_AUTH_URL=/mybusiness/auth

# 安全配置
VITE_ENABLE_HTTPS=true
```

## 🚀 使用方法

### 在代码中使用环境变量

```javascript
// 直接使用
const apiUrl = import.meta.env.VITE_API_BASE_URL;
const timeout = parseInt(import.meta.env.VITE_API_TIMEOUT);

// 使用默认值
const uploadMaxSize =
  parseInt(import.meta.env.VITE_UPLOAD_MAX_SIZE) || 10485760;
```

### 在 Vite 配置中使用

```javascript
import { defineConfig, loadEnv } from "vite";

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    server: {
      proxy: {
        "/mybusiness": {
          target: env.VITE_API_TARGET || "http://localhost:8080",
          changeOrigin: true,
        },
      },
    },
  };
});
```

### 类型安全的环境变量

如果使用 TypeScript，可以创建类型定义：

```typescript
// src/types/env.d.ts
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_TARGET: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_ENABLE_CONSOLE_LOG: string;
  readonly VITE_SHOW_DEBUG_INFO: string;
  readonly VITE_UPLOAD_MAX_SIZE: string;
  // 添加更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

## ⚙️ 配置建议

### 开发环境

- 启用详细日志：`VITE_ENABLE_CONSOLE_LOG=true`
- 显示调试信息：`VITE_SHOW_DEBUG_INFO=true`
- 较小的文件大小限制：`VITE_UPLOAD_MAX_SIZE=10485760` (10MB)
- 较短的超时时间：`VITE_API_TIMEOUT=10000` (10秒)

### 生产环境

- 禁用日志：`VITE_ENABLE_CONSOLE_LOG=false`
- 隐藏调试信息：`VITE_SHOW_DEBUG_INFO=false`
- 较大的文件大小限制：`VITE_UPLOAD_MAX_SIZE=20971520` (20MB)
- 较长的超时时间：`VITE_API_TIMEOUT=15000` (15秒)
- 启用安全特性：`VITE_ENABLE_HTTPS=true`

## 🔒 安全注意事项

1. **不要在环境变量中存储敏感信息**（如密钥、密码）
2. **以 `VITE_` 开头的变量会被暴露到客户端**，任何人都可以查看
3. **敏感配置应该在服务器端处理**，通过 API 获取
4. **生产环境配置应该通过 CI/CD 系统注入**，而不是提交到代码库

## 📝 自定义环境变量

如需添加新的环境变量：

1. **在环境文件中定义**：

   ```bash
   VITE_MY_CUSTOM_CONFIG=value
   ```

2. **在代码中使用**：

   ```javascript
   const myConfig = import.meta.env.VITE_MY_CUSTOM_CONFIG;
   ```

3. **添加到 apiConfig**（如果是 API 相关）：
   ```javascript
   const apiConfig = {
     // ... 其他配置
     myCustomConfig: import.meta.env.VITE_MY_CUSTOM_CONFIG || "default",
   };
   ```

## 🛠️ 调试

在开发过程中，可以通过以下方式检查环境变量：

```javascript
// 查看所有环境变量
console.log("环境变量:", import.meta.env);

// 查看 API 配置
import { apiConfig } from "@/utils/http.js";
console.log("API 配置:", apiConfig);
```
