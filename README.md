# MyBusinessV2

企业级数据与运营管理平台，基于 Vue 3 + Vite + Pinia + Tailwind CSS + Element Plus 构建，支持多角色权限、国际化、响应式布局和高性能前端工程实践。

---

## ✨ 核心特性

- **多角色权限系统**：基于 RTM（如 Mono、Multi、Carrier）动态控制路由和菜单可见性，支持子路由/菜单权限继承。
- **认证与授权**：应用启动时自动读取 sessionStorage 的认证信息，未认证自动跳转认证页。
- **响应式设计**：移动端与桌面端自适应，侧边栏、导航栏等布局自动切换。
- **国际化**：内置多语言（简体中文、繁体、英文等），可动态切换。
- **现代 UI/UX**：Tailwind CSS + Element Plus极致体验。
- **高性能工程**：Vite 极速开发与构建，自动代码分割，按需加载。
- **类型安全**：TypeScript 全面类型检查。
- **代码规范**：集成 ESLint、Prettier，自动格式化与校验。

---

## 🛠️ 技术栈

- **前端框架**：Vue 3 (Composition API, `<script setup>`)
- **路由管理**：Vue Router 4
- **状态管理**：Pinia
- **UI 框架**：Tailwind CSS、Element Plus
- **国际化**：vue-i18n
- **HTTP 请求**：Axios（统一拦截、错误处理、权限自动注入）
- **构建工具**：Vite
- **类型系统**：TypeScript

---

## 🚀 快速开始

### 1. 环境准备

- Node.js >= 16.x
- 使用 pnpm

### 2. 安装依赖

```bash
npm run buildDependencies // 构建组件库等依赖
pnpm install
```

### 3. 配置环境变量

参考 [ENV.md](./ENV.md) 创建 `.env.development` 和 `.env.production`，配置 API 地址、超时、上传等参数。

### 4. 设置认证信息（开发调试必读）

本项目依赖 sessionStorage 注入认证信息：

```js
sessionStorage.setItem("reseller-id", "YOUR_RESELLER_ID");
sessionStorage.setItem("reseller-rtm", "Mono"); // 或 Multi、Carrier
```

如未设置，将无法通过权限校验。

### 5. 启动开发服务器

```bash
npm run dev
```

### 6. 构建生产包

```bash
npm run build
```

---

## 🗂️ 目录结构

```
src/
  api/           # API 封装与服务
  assets/        # 静态资源
  packages/      # Monorepo子包，包括 dmp 组件库等
  components/    # 业务与基础组件
  config/        # 菜单、全局配置
  constants/     # 路由常量
  i18n/          # 国际化资源
  router/        # 路由配置与守卫
  stores/        # Pinia 状态管理
  utils/         # 工具函数
  views/         # 页面视图
  style.css      # Tailwind 全局样式
```

---

## 🛡️ 权限系统说明

- **权限来源**：`sessionStorage` 的 `reseller-rtm` 字段（如 Mono、Multi、Carrier）
- **菜单权限**：在 `config/menu.js` 的 permissions 配置
- **继承机制**：子路由/菜单未配置权限时自动继承父级
- **守卫机制**：路由守卫自动拦截无权限访问，跳转认证/无权限页面
- **菜单过滤**：Sidebar 仅渲染当前用户有权限的菜单项

详细用法见 [src/utils/permissions-demo.md](src/utils/permissions-demo.md)

---

## 📜 常用命令

| 命令                 | 说明                |
| -------------------- | ------------------- |
| `npm run dev`        | 启动开发服务器      |
| `npm run build`      | 构建生产包          |
| `npm run preview`    | 本地预览生产包      |
| `npm run lint`       | 代码风格检查        |
| `npm run format`     | 代码自动格式化      |
| `npm run type-check` | TypeScript 类型检查 |

---

## 🌍 国际化

- 支持多语言，语言包位于 `src/i18n/`
- 动态切换，自动记忆用户选择

---

## 🧩 组件与UI

- 所有业务组件均采用 `<script setup>` + Composition API
- UI 组件库：Element Plus + Tailwind CSS
- 响应式、无障碍、可定制

---

## ⚡ 性能与工程优化

- Vite 自动代码分割与按需加载
- 图片 WebP 优化、懒加载
- Lighthouse/WebPageTest 优化 Web Vitals
- 自动导入 API/组件，极简开发体验

---

## 🧑‍💻 贡献指南

1. Fork 本仓库，创建新分支
2. 保持代码风格一致（自动格式化、lint）
3. 提交 PR 前请确保通过所有检查
4. 详细描述你的变更和动机

---

## 📝 常见问题

- **Q: 启动后一直显示"无权限"？**  
  A: 请检查 sessionStorage 是否正确设置了 `reseller-id` 和 `reseller-rtm`。

- **Q: 如何新增页面/菜单？**  
  A: 参考 `src/constants/routes.js` 和 `src/config/menu.js`，并配置权限。

- **Q: 如何自定义环境变量？**  
  A: 参考 [ENV.md](./ENV.md) 并在代码中通过 `import.meta.env` 访问。

- **Q: 如何调试权限系统？**  
  A: 详见 [src/utils/permissions-demo.md](src/utils/permissions-demo.md)。

---

## 📄 License

本项目为 Apple GC Sales 内部项目，仅限授权团队成员使用。
