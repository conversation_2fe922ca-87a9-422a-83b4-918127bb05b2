/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    '(feat': 格式化数字)
    ArcProgress: typeof import('./src/components/ui/ArcProgress.vue')['default']
    AuthenticatingView: typeof import('./src/components/ui/AuthenticatingView.vue')['default']
    CardWithTitle: typeof import('./src/components/ui/CardWithTitle/CardWithTitle.vue')['default']
    ChartCard: typeof import('./src/components/dashboard/ChartCard.vue')['default']
    CheckMark: typeof import('./src/components/ui/PureNotify/components/CheckMark.vue')['default']
    CircularProgress: typeof import('./src/components/ui/CircularProgress.vue')['default']
    ComingSoon: typeof import('./src/components/ui/ComingSoon/ComingSoon.vue')['default']
    CrossMark: typeof import('./src/components/ui/PureNotify/components/CrossMark.vue')['default']
    DashboardCard001: typeof import('./src/components/ui/DashboardCard001/DashboardCard001.vue')['default']
    DashboardCard002: typeof import('./src/components/ui/DashboardCard002/DashboardCard002.vue')['default']
    DashboardCard003: typeof import('./src/components/ui/DashboardCard003/DashboardCard003.vue')['default']
    DashboardCard004: typeof import('./src/components/ui/DashboardCard004/DashboardCard004.vue')['default']
    DashboardCard005: typeof import('./src/components/ui/DashboardCard005/DashboardCard005.vue')['default']
    DashboardCard006: typeof import('./src/components/ui/DashboardCard006/DashboardCard006.vue')['default']
    DashboardCard007: typeof import('./src/components/ui/DashboardCard007/DashboardCard007.vue')['default']
    DashboardCard008: typeof import('./src/components/ui/DashboardCard008/DashboardCard008.vue')['default']
    DmpAutoScroller: typeof import('@dmp/components')['DmpAutoScroller']
    DmpBarChart: typeof import('@dmp/components')['DmpBarChart']
    DmpBarStackChart: typeof import('@dmp/components')['DmpBarStackChart']
    DmpCycleSelector: typeof import('@dmp/components')['DmpCycleSelector']
    DmpDataCenterCard1x1: typeof import('@dmp/components')['DmpDataCenterCard1x1']
    DmpDataCenterCard2x1: typeof import('@dmp/components')['DmpDataCenterCard2x1']
    DmpDetailTable: typeof import('@dmp/components')['DmpDetailTable']
    DmpFlexCard: typeof import('@dmp/components')['DmpFlexCard']
    DmpFlexColsCard1x1x1: typeof import('@dmp/components')['DmpFlexColsCard1x1x1']
    DmpFooter: typeof import('@dmp/components')['DmpFooter']
    DmpGlassCard: typeof import('@dmp/components')['DmpGlassCard']
    DmpIcon: typeof import('@dmp/components')['DmpIcon']
    DmpInfoDetailTable: typeof import('@dmp/components')['DmpInfoDetailTable']
    DmpLineChart: typeof import('@dmp/components')['DmpLineChart']
    DmpPopover: typeof import('@dmp/components')['DmpPopover']
    DmpPopoverRank: typeof import('@dmp/components')['DmpPopoverRank']
    DmpSidebar: typeof import('@dmp/components')['DmpSidebar']
    DmpSidebarMobile: typeof import('@dmp/components')['DmpSidebarMobile']
    DmpSiderbarMobile: typeof import('@dmp/components')['DmpSiderbarMobile']
    DmpSimpleDropdown: typeof import('@dmp/components')['DmpSimpleDropdown']
    DmpTabSwitcher: typeof import('@dmp/components')['DmpTabSwitcher']
    DmpTextList: typeof import('@dmp/components')['DmpTextList']
    DmpValueWithUnit: typeof import('@dmp/components')['DmpValueWithUnit']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElementTest: typeof import('./src/components/ui/ElementTest.vue')['default']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    FeatureCard: typeof import('./src/components/home/<USER>')['default']
    GlassCard: typeof import('./src/components/ui/GlassCard.vue')['default']
    Icon: typeof import('./packages/components/src/icon/icon.vue')['default']
    InfoMark: typeof import('./src/components/ui/PureNotify/components/InfoMark.vue')['default']
    ItemWithArrow: typeof import('./src/components/ui/ItemWithArrow/ItemWithArrow.vue')['default']
    NavBar: typeof import('./src/components/layout/NavBar.vue')['default']
    OpsMetricCard: typeof import('./src/components/ui/DashboardCard002/OpsMetricCard.vue')['default']
    PureNotify: typeof import('./src/components/ui/PureNotify/PureNotify.vue')['default']
    QuickAlertCard: typeof import('./src/components/ui/DashboardCard001/QuickAlertCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    SidebarToggle: typeof import('./src/components/layout/SidebarToggle.vue')['default']
    StatCard: typeof import('./src/components/dashboard/StatCard.vue')['default']
    TabSwitcher: typeof import('./src/components/ui/TabSwitcher.vue')['default']
    ValueWithUnit: typeof import('./src/components/ui/ValueWithUnit/ValueWithUnit.vue')['default']
    WelcomeCard: typeof import('./src/components/home/<USER>')['default']
  }
}
