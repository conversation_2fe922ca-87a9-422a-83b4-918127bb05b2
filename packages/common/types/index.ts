// import type { Component } from 'vue'

type Component = any // TODO 后续替换成上面

export type CardSchema = {
  id: string,
  type: string,
  title: string,
  api: string,
}

export type GroupSchema = {
  id: string,
  type: string,
  title: string,
  children: CardSchema[]
}

export interface Schema {
  layout: {
    desktop: GroupSchema[],
    mobile: GroupSchema[]
  },
  popovers: Record<string, {
    type: string;
    title: string;
    extraIcon?: string;
    extraTitle?: string;
    props: Record<string, any>
  }>
}

export type CompRegistry = Record<string, Component>