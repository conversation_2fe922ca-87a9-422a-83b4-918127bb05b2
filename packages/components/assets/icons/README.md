# SVG 图标使用指南

## 📁 文件夹结构

```
src/assets/icons/
├── toggle-sidebar.svg    # 侧边栏切换图标
├── home.svg             # 首页图标 (你可以添加)
├── dashboard.svg        # 仪表板图标 (你可以添加)
└── ...                  # 其他图标
```

### 方法一：作为 Vue 组件使用（推荐）

```vue
<script setup>
import HomeIcon from "@/assets/icons/home.svg";
import DashboardIcon from "@/assets/icons/dashboard.svg";
</script>

<template>
  <div>
    <!-- 直接使用 -->
    <HomeIcon class="w-5 h-5 text-blue-600" />

    <!-- 带样式 -->
    <DashboardIcon class="w-6 h-6 text-gray-500 hover:text-gray-700" />
  </div>
</template>
```

### 方法二：作为 URL 使用

```vue
<script setup>
import homeIconUrl from "@/assets/icons/home.svg?url";
</script>

<template>
  <img :src="homeIconUrl" alt="Home" class="w-5 h-5" />
</template>
```

### 方法三：作为原始 SVG 字符串

```vue
<script setup>
import homeIconRaw from "@/assets/icons/home.svg?raw";
</script>

<template>
  <div v-html="homeIconRaw"></div>
</template>
```

## 🎯 最佳实践

### 1. 统一的图标尺寸

- 导出时保持一致的画板尺寸（推荐 24x24px）
- 使用 Tailwind 的尺寸类：`w-4 h-4`, `w-5 h-5`, `w-6 h-6`

### 2. 优化 SVG 文件

- 移除不必要的属性（width, height 在代码中控制）
- 使用 `currentColor` 让图标继承文本颜色
- 保持简洁的路径

### 3. 命名规范

- 使用kebab-case：`user-profile.svg`
- 语义化命名：`arrow-left.svg`, `close-modal.svg`

## 🔧 高级用法

### 动态图标

```vue
<script setup>
import { computed } from "vue";

const iconName = ref("home");
const DynamicIcon = computed(() => {
  return () => import(`@/assets/icons/${iconName.value}.svg`);
});
</script>

<template>
  <component :is="DynamicIcon" class="w-5 h-5" />
</template>
```

### 图标库组件

```vue
<!-- IconLibrary.vue -->
<script setup>
const props = defineProps({
  name: String,
  size: {
    type: String,
    default: "w-5 h-5",
  },
});

const icons = {
  home: () => import("@/assets/icons/home.svg"),
  dashboard: () => import("@/assets/icons/dashboard.svg"),
  // ... 更多图标
};
</script>

<template>
  <component :is="icons[name]" :class="size" v-if="icons[name]" />
</template>
```

## 🎨 SVG 优化建议

从 Sketch 导出后，建议手动优化 SVG：

```svg
<!-- 优化前 -->
<svg width="24" height="24" viewBox="0 0 24 24" fill="#000000">
  <path d="..."/>
</svg>

<!-- 优化后 -->
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="..."/>
</svg>
```

这样图标就可以通过 CSS 的 `color` 属性来控制颜色了！
