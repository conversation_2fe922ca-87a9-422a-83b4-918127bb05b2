<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>􀏚</title>
    <defs>
        <linearGradient x1="18.3813477%" y1="14.5446777%" x2="82.7587891%" y2="84.7766113%" id="linearGradient-1">
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="3.03289251%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="97.0894884%"></stop>
            <stop stop-color="#F1F1F1" offset="100%"></stop>
        </linearGradient>
        <path d="M20,0 C31.045695,-3.55271368e-15 40,8.954305 40,20 C40,31.045695 31.045695,40 20,40 C8.954305,40 0,31.045695 0,20 C0,8.954305 8.954305,0 20,0 Z" id="path-2"></path>
        <filter x="-31.2%" y="-28.8%" width="162.5%" height="162.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feComposite in="shadowBlurOuter2" in2="SourceAlpha" operator="out" result="shadowBlurOuter2"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-26.2%" y="-23.8%" width="152.5%" height="152.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0803376311 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Homepage-Solution-B" transform="translate(-219, -12)">
            <g id="top" transform="translate(200, 0)">
                <g id="画框" transform="translate(19, 12)" xlink:href="#path-2">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill-opacity="0.1" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                    <path stroke="url(#linearGradient-1)" stroke-width="1" d="M20,0.5 C25.3847763,0.5 30.2597763,2.68261184 33.7885822,6.21141777 C37.3173882,9.74022369 39.5,14.6152237 39.5,20 C39.5,25.3847763 37.3173882,30.2597763 33.7885822,33.7885822 C30.2597763,37.3173882 25.3847763,39.5 20,39.5 C14.6152237,39.5 9.74022369,37.3173882 6.21141777,33.7885822 C2.68261184,30.2597763 0.5,25.3847763 0.5,20 C0.5,14.6152237 2.68261184,9.74022369 6.21141777,6.21141777 C9.74022369,2.68261184 14.6152237,0.5 20,0.5 Z" stroke-linejoin="square"></path>
                    <g id="􀏚" stroke-width="1" fill-rule="evenodd" transform="translate(10, 10)" fill="#1C1C1E">
                        <path d="M4.27001953,16.5600586 L16.809082,16.5600586 C17.6049805,16.5600586 18.2055664,16.3586426 18.6108398,15.9558105 C19.0161133,15.5529785 19.21875,14.9609375 19.21875,14.1796875 L19.21875,5.26611328 C19.21875,4.47998047 19.0161133,3.88671875 18.6108398,3.48632812 C18.2055664,3.0859375 17.6049805,2.88574219 16.809082,2.88574219 L4.27001953,2.88574219 C3.47900391,2.88574219 2.88085938,3.0859375 2.47558594,3.48632812 C2.0703125,3.88671875 1.86767578,4.47998047 1.86767578,5.26611328 L1.86767578,14.1796875 C1.86767578,14.9609375 2.0703125,15.5529785 2.47558594,15.9558105 C2.88085938,16.3586426 3.47900391,16.5600586 4.27001953,16.5600586 Z M4.35791016,15.1171875 C4.01611328,15.1171875 3.75610352,15.0292969 3.57788086,14.8535156 C3.3996582,14.6777344 3.31054688,14.4116211 3.31054688,14.0551758 L3.31054688,5.38330078 C3.31054688,5.03173828 3.3996582,4.76806641 3.57788086,4.59228516 C3.75610352,4.41650391 4.01611328,4.32861328 4.35791016,4.32861328 L16.7285156,4.32861328 C17.0654297,4.32861328 17.3242188,4.41650391 17.5048828,4.59228516 C17.6855469,4.76806641 17.7758789,5.03173828 17.7758789,5.38330078 L17.7758789,14.0551758 C17.7758789,14.4116211 17.6855469,14.6777344 17.5048828,14.8535156 C17.3242188,15.0292969 17.0654297,15.1171875 16.7285156,15.1171875 L4.35791016,15.1171875 Z M7.69042969,15.3515625 L9.06005859,15.3515625 L9.06005859,4.07958984 L7.69042969,4.07958984 L7.69042969,15.3515625 Z M6.25488281,7.00927734 C6.38671875,7.00927734 6.50024414,6.96044922 6.59545898,6.86279297 C6.69067383,6.76513672 6.73828125,6.65771484 6.73828125,6.54052734 C6.73828125,6.41357422 6.69067383,6.30371094 6.59545898,6.2109375 C6.50024414,6.11816406 6.38671875,6.07177734 6.25488281,6.07177734 L4.75341797,6.07177734 C4.62646484,6.07177734 4.51538086,6.11816406 4.42016602,6.2109375 C4.32495117,6.30371094 4.27734375,6.41357422 4.27734375,6.54052734 C4.27734375,6.65771484 4.32495117,6.76513672 4.42016602,6.86279297 C4.51538086,6.96044922 4.62646484,7.00927734 4.75341797,7.00927734 L6.25488281,7.00927734 Z M6.25488281,8.97216797 C6.38671875,8.97216797 6.50024414,8.92456055 6.59545898,8.8293457 C6.69067383,8.73413086 6.73828125,8.62304688 6.73828125,8.49609375 C6.73828125,8.37402344 6.69067383,8.26660156 6.59545898,8.17382812 C6.50024414,8.08105469 6.38671875,8.03466797 6.25488281,8.03466797 L4.75341797,8.03466797 C4.62646484,8.03466797 4.51538086,8.08105469 4.42016602,8.17382812 C4.32495117,8.26660156 4.27734375,8.37402344 4.27734375,8.49609375 C4.27734375,8.62304688 4.32495117,8.73413086 4.42016602,8.8293457 C4.51538086,8.92456055 4.62646484,8.97216797 4.75341797,8.97216797 L6.25488281,8.97216797 Z M6.25488281,10.9277344 C6.38671875,10.9277344 6.50024414,10.8813477 6.59545898,10.7885742 C6.69067383,10.6958008 6.73828125,10.5883789 6.73828125,10.4663086 C6.73828125,10.3393555 6.69067383,10.2294922 6.59545898,10.1367188 C6.50024414,10.0439453 6.38671875,9.99755859 6.25488281,9.99755859 L4.75341797,9.99755859 C4.62646484,9.99755859 4.51538086,10.0439453 4.42016602,10.1367188 C4.32495117,10.2294922 4.27734375,10.3393555 4.27734375,10.4663086 C4.27734375,10.5883789 4.32495117,10.6958008 4.42016602,10.7885742 C4.51538086,10.8813477 4.62646484,10.9277344 4.75341797,10.9277344 L6.25488281,10.9277344 Z" id="形状" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>