<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>􀏚</title>
    <defs>
        <linearGradient x1="18.3813477%" y1="14.5446777%" x2="82.7587891%" y2="84.7766113%" id="linearGradient-1">
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#F7F7F7" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="3.03289251%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="97.0894884%"></stop>
            <stop stop-color="#F1F1F1" offset="100%"></stop>
        </linearGradient>
        <path d="M20,0 C31.045695,-3.55271368e-15 40,8.954305 40,20 C40,31.045695 31.045695,40 20,40 C8.954305,40 0,31.045695 0,20 C0,8.954305 8.954305,0 20,0 Z" id="path-2"></path>
        <filter x="-31.2%" y="-28.8%" width="162.5%" height="162.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feComposite in="shadowBlurOuter2" in2="SourceAlpha" operator="out" result="shadowBlurOuter2"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.04 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-26.2%" y="-23.8%" width="152.5%" height="152.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0803376311 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Homepage-Solution-B" transform="translate(-219, -12)">
            <g id="top" transform="translate(200, 0)">
                <g id="画框" transform="translate(19, 12)" xlink:href="#path-2">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill-opacity="0.1" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                    <path stroke="url(#linearGradient-1)" stroke-width="1" d="M20,0.5 C25.3847763,0.5 30.2597763,2.68261184 33.7885822,6.21141777 C37.3173882,9.74022369 39.5,14.6152237 39.5,20 C39.5,25.3847763 37.3173882,30.2597763 33.7885822,33.7885822 C30.2597763,37.3173882 25.3847763,39.5 20,39.5 C14.6152237,39.5 9.74022369,37.3173882 6.21141777,33.7885822 C2.68261184,30.2597763 0.5,25.3847763 0.5,20 C0.5,14.6152237 2.68261184,9.74022369 6.21141777,6.21141777 C9.74022369,2.68261184 14.6152237,0.5 20,0.5 Z" stroke-linejoin="square"></path>
                    <text id="􀏚" font-family="SFPro-Medium, SF Pro" font-size="15" font-weight="400" line-spacing="20" fill="#1C1C1E">
                        <tspan x="10" y="24">􀏚</tspan>
                    </text>
                </g>
            </g>
        </g>
    </g>
</svg>