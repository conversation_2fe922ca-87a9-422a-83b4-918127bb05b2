/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
