declare module "vue" {
  export interface GlobalComponents {
    DmpSidebar: (typeof import("@dmp/components"))["DmpSidebar"];
    DmpSidebarMobile: (typeof import("@dmp/components"))["DmpSidebarMobile"];
    DmpFlexCard: (typeof import("@dmp/components"))["DmpFlexCard"];
    DmpFlexColsCard1x1x1: (typeof import("@dmp/components"))["DmpFlexColsCard1x1x1"];
    DmpGlassCard: (typeof import("@dmp/components"))["DmpGlassCard"];
  }

  interface ComponentCustomProperties {}
}

export {};
