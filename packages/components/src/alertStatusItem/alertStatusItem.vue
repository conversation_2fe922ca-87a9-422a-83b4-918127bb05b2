<script setup lang="ts">
import { AlertStatusItemProps } from "./props";
import { DmpProgressBar } from "../progressBar";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";

defineOptions({
  name: "DmpAlertStatusItem",
});

const emit = defineEmits(["itemClick"]);

const props = withDefaults(defineProps<AlertStatusItemProps>(), {
  id: "",
  title: "",
  statusText: "",
  gradient: () => ["#FF8175", "#F63F54"],
  statusLevel: "normal",
  progress: 0,
  color: "",
  showText: true,
  progressUnit: "",
  warningCnt: 0,
  textColor: "rgba(28,28,30,0.56)",
});

const handleClick = (id: string) => {
  emit("itemClick", id);
};
</script>

<template>
  <DmpPopoverTrigger :id="`${props.id}`">
    <div
      class="alert-status-item flex gap-[32px] py-[10px] bg-transparent justify-between highlightable"
      @click="handleClick(props.id)"
    >
      <div class="flex gap-[22px]">
        <div class="alert-status-item-title">{{ props.title }}</div>

        <div class="progress-bar">
          <DmpProgressBar v-bind="props" />
        </div>
      </div>

      <div class="alert-status-item-status-text" :style="{ color: props.textColor }">
        {{ props.statusText }}
      </div>
    </div>
  </DmpPopoverTrigger>
</template>
