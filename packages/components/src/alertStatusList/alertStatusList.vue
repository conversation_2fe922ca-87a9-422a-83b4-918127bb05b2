<script setup lang="ts">
import { AlertStatusListProps } from "./props";
import DmpAlertStatusItem from "@/alertStatusItem";
import { ref, nextTick } from "vue";
import { onClickOutside } from "@vueuse/core";

defineOptions({
  name: "DmpAlertStatusList",
});

const props = withDefaults(defineProps<AlertStatusListProps>(), {
  items: () => [],
});

const containerRef = ref();
const currentId = ref("");

const handleItemClick = async (id: string) => {
  currentId.value = id;
};

onClickOutside(containerRef, () => {
  currentId.value = "";
});
</script>

<template>
  <div ref="containerRef" class="alert-status-list">
    <el-scrollbar class="h-full">
      <div class="relative">
        <div
          v-for="item in props.items"
          :key="item.id"
          :data-item-id="item.id"
          :class="[
            'relative z-10 px-[8px] rounded-[8px]',
            currentId === item.id ? 'bg-white' : 'bg-transparent',
          ]"
        >
          <dmp-alert-status-item
            :id="item.id"
            :title="item.title"
            :progress="item.progress"
            :color="item.color"
            :status-text="item.statusText"
            :gradient="item.gradient"
            :status-level="item.statusLevel"
            :show-text="item.showText"
            :progress-unit="item.progressUnit"
            :warning-cnt="item.warningCnt"
            :text-color="item.textColor"
            @itemClick="handleItemClick"
          />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
