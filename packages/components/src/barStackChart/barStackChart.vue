<script setup lang="ts">
import { computed } from "vue";
import * as echarts from "echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { BarStackChartProps } from "@/barStackChart/props";
use([
  Canvas<PERSON><PERSON>er,
  Bar<PERSON>hart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
]);
defineOptions({ name: "DmpBarStack<PERSON>hart" });
const props = defineProps<BarStackChartProps>();

const colorList = [
  "#FF5B6A", // 非常严重
  "#FFA940", // 严重
  "#59ADC4", // 中等
];

const finalOption = computed(() => ({
  grid: {
    left: 8,
    right: 8,
    top: 8,
    bottom: 8,
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 6,
    textStyle: { color: "#6E6E73", fontSize: 11 },
  },
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: props.xAxisData,
    axisLine: {
      show: true,
      lineStyle: {
        color: "#E5E5EA",
        width: 3,
        cap: "round",
      },
      z: 10,
    },
    axisTick: {
      show: true,
      alignWithLabel: true,
      length: 8,
      lineStyle: {
        color: "#E5E5EA",
        width: 3,
        cap: "round",
      },
    },
    axisLabel: {
      color: "#AEAEB2",
      fontSize: 11,
      fontWeight: 400,
      margin: 10,
      align: "center",
      padding: [0, 0, 0, 0],
    },
    splitLine: { show: false },
  },
  yAxis: {
    type: "value",
    min: 0,
    max: 100,
    interval: 20,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: "#AEAEB2",
      fontSize: 11,
      margin: 2,
      align: "right",
      padding: [0, 4, 0, 0],
      formatter: function (val: number) {
        return val.toLocaleString();
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "#E5E6EB",
        type: "dashed",
        width: 1,
      },
    },
  },
  series: props.series.map((s, i) => ({
    name: props.legendNames?.[i] || s.name,
    type: "bar",
    stack: "total",
    data: s.data,
    barWidth: 6,
    itemStyle: {
      color: s.color || colorList[i % colorList.length],
      borderRadius: i === props.series.length - 1 ? [2, 2, 0, 0] : [0, 0, 0, 0],
    },
    emphasis: {
      focus: "series",
    },
  })),
}));
</script>

<template>
  <div>
    <div
      v-if="props.title"
      class="text-[12px] font-medium text-[#1C1C1E] leading-[20px] mb-3"
    >
      {{ props.title }}
    </div>
    <div class="flex items-center justify-between mb-3">
      <div
        v-if="props.subtitle"
        class="text-[11px] font-normal text-[#1C1C1E] leading-[16px]"
      >
        {{ props.subtitle }}
      </div>
      <div class="flex items-center">
        <template
          v-for="(name, i) in props.legendNames ||
          props.series.map((s) => s.name)"
          :key="name"
        >
          <span
            class="inline-block w-2 h-2 rounded-full mr-1"
            :style="{
              background:
                props.series[i]?.color || colorList[i % colorList.length],
            }"
          ></span>
          <span
            class="text-[11px] font-normal text-[#6E6E73] leading-[16px] mr-2"
            >{{ name }}</span
          >
        </template>
      </div>
    </div>
    <v-chart
      :option="finalOption"
      autoresize
      style="height: 155px; width: 100%"
    />
  </div>
</template>
