import { computed } from 'vue'
import type { CircleProgressProps } from '@/circleProgress/props'

export const useCircleProgress = (props: CircleProgressProps) => {
  // 计算安全边距，确保圆点不会被切掉
  const safeMargin = computed(
    () => Math.max((props.strokeWidth || 6) / 2, (props.dotSize || 8) / 2) + 2,
  );
  
  // 弧线的半径，考虑安全边距
  const radius = computed(() => (100 - safeMargin.value * 2) / 2);
  
  // 圆心坐标，考虑安全边距
  const centerX = computed(() => 50);
  const centerY = computed(() => 50);
  
  // 根据开口方向计算起始角度和结束角度
  const startAngle = computed(() => {
    const halfArc = (props.arcAngle || 270) / 2;
    let centerAngle;
  
    switch (props.openDirection || "bottom") {
      case "top":
        centerAngle = -90; // 顶部开口，中心在上方
        break;
      case "bottom":
        centerAngle = 90; // 底部开口，中心在下方
        break;
      case "left":
        centerAngle = 180; // 左侧开口，中心在左方
        break;
      case "right":
        centerAngle = 0; // 右侧开口，中心在右方
        break;
      default:
        centerAngle = 90;
    }
  
    return centerAngle - halfArc;
  });

  const endAngle = computed(() => {
    const halfArc = (props.arcAngle || 270) / 2;
    let centerAngle;
  
    switch (props.openDirection || "bottom") {
      case "top":
        centerAngle = -90;
        break;
      case "bottom":
        centerAngle = 90;
        break;
      case "left":
        centerAngle = 180;
        break;
      case "right":
        centerAngle = 0;
        break;
      default:
        centerAngle = 90;
    }
  
    return centerAngle + halfArc;
  });
  
  // 创建弧线路径的函数
  const createArcPath = (startAngle: number, endAngle: number) => {
    const startRadian = (startAngle * Math.PI) / 180;
    const endRadian = (endAngle * Math.PI) / 180;
  
    const startX = centerX.value + radius.value * Math.cos(startRadian);
    const startY = centerY.value + radius.value * Math.sin(startRadian);
    const endX = centerX.value + radius.value * Math.cos(endRadian);
    const endY = centerY.value + radius.value * Math.sin(endRadian);
  
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
  
    return `M ${startX} ${startY} A ${radius.value} ${radius.value} 0 ${largeArcFlag} 1 ${endX} ${endY}`;
  };
  
  // 背景弧线路径
  const backgroundPath = computed(() => createArcPath(startAngle.value, endAngle.value));
  
  // 进度弧线路径
  const progressPath = computed(() => createArcPath(startAngle.value, endAngle.value));
  
  // 进度弧线的长度
  const arcLength = computed(() => {
    const angle = ((endAngle.value - startAngle.value) * Math.PI) / 180;
    return radius.value * angle;
  });
  
  // 进度dasharray
  const progressDashArray = computed(
    () => `${arcLength.value} ${arcLength.value}`,
  );
  
  // 进度dashoffset
  const progressDashOffset = computed(() => {
    const progressLength = (arcLength.value * (props.progress || 0)) / 100;
    return arcLength.value - progressLength;
  });
  
  // 圆点位置
  const dotPosition = computed(() => {
    if ((props.progress || 0) <= 0) return { x: 0, y: 0 };
  
    const angle = startAngle.value + (endAngle.value - startAngle.value) * ((props.progress || 0) / 100);
    const radian = (angle * Math.PI) / 180;
  
    return {
      x: centerX.value + radius.value * Math.cos(radian),
      y: centerY.value + radius.value * Math.sin(radian),
    };
  });

  return {
    backgroundPath,
    progressPath,
    progressDashArray,
    progressDashOffset,
    dotPosition,
    startAngle,
    endAngle,
  };
};