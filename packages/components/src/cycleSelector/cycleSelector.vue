<template>
  <div class="dmp-cycle-selector relative w-full h-8 flex items-center">
    <!-- 遮罩，略大于按钮宽度 -->
    <!-- <div
      v-if="showLeftMask"
      class="pointer-events-none absolute z-10 top-0 left-0 h-8"
      style="
        width: 40px;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0.98) 40%,
          transparent
        );
      "
    ></div> -->
    <!-- 箭头按钮 -->
    <button
      class="absolute left-0 top-0 w-6 h-8 flex items-center justify-center rounded-[8px] transition z-20"
      :disabled="isFirst"
      @click="prev"
      aria-label="上一周期"
      :class="[isFirst ? 'bg-[rgba(28,28,30,0.06)]' : 'bg-white']"
    >
      <el-icon :size="16" :style="{ color: isFirst ? '#6E6E73' : '#1C1C1E' }">
        <ArrowLeftBold />
      </el-icon>
    </button>
    <div
      class="flex-1 flex items-center overflow-x-auto scrollbar-hide cursor-pointer ml-[24px] mr-[24px] min-w-0"
      ref="listRef"
      @scroll="onScroll"
      style="height: 100%"
    >
      <template v-for="(item, idx) in cycles" :key="item">
        <button
          class="px-[10px] py-0 h-6 flex items-center text-[11px] regular min-w-fit"
          :class="[
            idx === selectedIndex
              ? 'bg-white shadow-[0_1px_4px_0_rgba(0,0,0,0.04)] border border-[#e5e6eb] font-medium text-[#0071E3] rounded-full'
              : 'bg-transparent text-[#3A3A3C] font-normal',
          ]"
          @click="select(idx)"
          :ref="(el) => (cycleRefs[idx] = el as HTMLElement)"
        >
          {{ item }}
        </button>
      </template>
    </div>
    <!-- 右遮罩 -->
    <!-- <div
      v-if="showRightMask"
      class="pointer-events-none absolute z-10 top-0 right-0 h-8"
      style="
        width: 40px;
        background: linear-gradient(
          to left,
          rgba(255, 255, 255, 0.98) 40%,
          transparent
        );
      "
    ></div> -->
    <!-- 右箭头 -->
    <button
      class="absolute right-0 top-0 w-6 h-8 flex items-center justify-center rounded-[8px] transition z-20"
      :disabled="isLast"
      @click="next"
      aria-label="下一周期"
      :class="[isLast ? 'bg-[rgba(28,28,30,0.06)]' : 'bg-white']"
    >
      <el-icon :size="16" :style="{ color: isLast ? '#6E6E73' : '#1C1C1E' }">
        <ArrowRightBold />
      </el-icon>
    </button>
  </div>
  <el-divider class="my-[12px] mx-0" />
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, nextTick } from "vue";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import { Props } from "@/cycleSelector/props";

defineOptions({ name: "DmpCycleSelector" });
const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();

const selectedIndex = ref(
  props.modelValue ? props.cycles.findIndex((c) => c === props.modelValue) : 0
);

watch(
  () => props.modelValue,
  (val) => {
    if (val && props.cycles.includes(val)) {
      selectedIndex.value = props.cycles.findIndex((c) => c === val);
    }
  }
);

const isFirst = computed(() => selectedIndex.value <= 0);
const isLast = computed(() => selectedIndex.value >= props.cycles.length - 1);

const cycleRefs = ref<HTMLElement[]>([]);
const listRef = ref<HTMLElement | null>(null);

watch(
  () => selectedIndex.value,
  async (val) => {
    await nextTick();
    const el = cycleRefs.value[val];
    const list = listRef.value;
    if (el && list) {
      const elLeft = el.offsetLeft;
      const elWidth = el.offsetWidth;
      const listWidth = list.clientWidth;
      const scrollTo = elLeft - (listWidth - elWidth) / 2;
      list.scrollTo({ left: scrollTo, behavior: "smooth" });
    }
  }
);

function select(idx: number) {
  if (idx < 0 || idx >= props.cycles.length) return;
  selectedIndex.value = idx;
  emit("update:modelValue", props.cycles[idx]);
  emit("change", props.cycles[idx]);
}

function prev() {
  if (!isFirst.value) select(selectedIndex.value - 1);
}
function next() {
  if (!isLast.value) select(selectedIndex.value + 1);
}

const showLeftMask = ref(false);
const showRightMask = ref(false);

function updateMask() {
  const el = listRef.value;
  if (!el) return;
  showLeftMask.value = el.scrollLeft > 0;
  showRightMask.value = el.scrollLeft + el.clientWidth < el.scrollWidth - 1;
}

function onScroll() {
  updateMask();
}

watch(
  () => props.cycles,
  () => nextTick(updateMask)
);
watch(
  () => selectedIndex.value,
  async () => {
    await nextTick();
    updateMask();
  }
);
</script>
