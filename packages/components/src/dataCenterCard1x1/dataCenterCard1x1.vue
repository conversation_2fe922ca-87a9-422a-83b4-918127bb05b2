<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpValueWithUnit from "@/valueWithUnit/ValueWithUnit.vue";
import DmpAutoScroller from "@/autoScroller/autoScroller.vue";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";
import { computed, toRefs, withDefaults } from "vue";
import { DmpDataCenterCard1x1Props } from "./props.ts";

defineOptions({ name: "DmpDataCenterCard1x1" });
const props = withDefaults(defineProps<DmpDataCenterCard1x1Props>(), {
  id: "",
  showArrow: false,
  countClass: () => [],
  unitClass: () => [],
  list: () => [],
});
const { id, updateTime, list, title, showArrow, countClass, unitClass } = toRefs(props);

// 容器布局占位
const gridSpan = computed(() => {
  return { row: 1, col: list.value.length > 2 ? 2 : 1 };
});

const colStyle = computed(() => {
  return {
    "grid-template-columns": `repeat(${list.value.length > 2 ? list.value.length : 1}, minmax(25%, 1fr))`,
  };
});

const itemClass = computed(() => {
  if (list.value.length > 2) {
    return ["flex-col-reverse", "gap-[4px]", "items-center", "vertical-line"];
  }
  return ["flex-col", "gap-[2px]", list.value.length === 2 ? "horizontal-line" : ""];
});

const selfCountClass = computed(() => {
  switch (list.value.length) {
    case 1:
      return ["text-[24px]", "leading-[24px]"];
    case 2:
      return ["text-[18px]", "leading-[18px]"];
    default:
      return ["text-[16px]", "leading-[16px]"];
  }
});

const showIcon = computed(() => {
  return list.value.length > 2;
});

const showUpdateTime = computed(() => {
  return list.value.length !== 2 && updateTime.value;
});
</script>

<template>
  <DmpFlexCard :title="title" :grid-span="gridSpan" :show-arrow="showArrow">
    <div class="flex flex-col h-full relative">
      <DmpAutoScroller horizontal>
        <div class="grid" :style="colStyle">
          <template v-for="item in list" :key="item.name">
            <DmpPopoverTrigger :id="`${id}.${item.name}`">
              <div
                class="flex relative p-[8px] rounded-[12px] highlightable"
                :class="itemClass"
              >
                <div class="text-[12px] leading-[16px] text-[rgba(28,28,30,.64)]">
                  {{ item.name }}
                </div>
                <DmpValueWithUnit
                  :value="item.count"
                  :unit="item.unit"
                  :value-class="[...selfCountClass, ...countClass]"
                  :unit-class="unitClass"
                />
                <div v-if="showIcon" class="w-[20px] h-[20px] mb-[4px]">
                  <img v-if="item.icon" :src="item.icon" class="w-full h-full" alt="" srcset="" />
                </div>
              </div>
            </DmpPopoverTrigger>
          </template>
        </div>
        <template #indicator="{ atStart, atEnd, scrollable }">
          <div
            v-if="scrollable"
            class="scroll-indicator absolute box-border bottom-[4px] w-[48px] right-[8px] px-[11px] h-[16px] rounded-[8px] bg-[rgba(28,28,30,.07)] flex items-center justify-between gap-[6px]"
            :class="{ 'at-end': atEnd || !atStart }"
          >
            <div
              class="h-[4px] rounded-full bg-[rgba(28,28,30,0.4)] transition-all duration-200 ease-linear"
              v-for="item in 2"
              :key="item"
            ></div>
          </div>
        </template>
      </DmpAutoScroller>

      <div
        v-if="showUpdateTime"
        class="mt-auto px-[8px] py-[4px] text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
      >
        {{ updateTime }}
      </div>
    </div>
  </DmpFlexCard>
</template>
