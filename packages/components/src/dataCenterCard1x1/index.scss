:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }

  .content {
    flex: 1;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}

.vertical-line + .vertical-line {
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 1px;
    height: 48px;
    background: rgba(0, 0, 0, 0.04);
  }
}

.horizontal-line + .horizontal-line {
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 8px;
    right: 8px;
    height: 1px;
    background: rgba(0, 0, 0, 0.04);
  }
}

.scroll-indicator {
  div:first-child {
    width: 16px;
    transform-origin: left;
  }
  div:last-child {
    width: 4px;
    transform-origin: right;
  }
  &.at-end {
    div:first-child {
      width: 4px;
    }
    div:last-child {
      width: 16px;
    }
  }
}
