export interface DmpDataCenterCard1x1Props {
  id: string;
  title: string;
  updateTime: string;
  showArrow?: boolean;
  countClass?: string[];
  unitClass?: string[];
  list: DmpDataCenterCard1x1ListItem[];
}

export interface DmpDataCenterCard1x1ListItem {
  name: string;
  count: number;
  unit: string;
  icon: string;
}

export type DmpDataCenterCard1x1Emits = {
  click: [item: DmpDataCenterCard1x1ListItem];
};
