<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpValueWithUnit from "@/valueWithUnit/ValueWithUnit.vue";
import DmpAutoScroller from "@/autoScroller/autoScroller.vue";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";
import { computed, toRefs, withDefaults } from "vue";
import { DmpDataCenterCard2x1Emits, DmpDataCenterCard2x1Props } from "./props.ts";

defineOptions({ name: "DmpDataCenterCard2x1" });
const emit = defineEmits<DmpDataCenterCard2x1Emits>();
const props = withDefaults(defineProps<DmpDataCenterCard2x1Props>(), {
  id: "",
  isMobile: false,
  showArrow: false,
  countClass: () => [],
  unitClass: () => [],
  list: () => [],
});
const { isMobile, updateTime, list, showArrow, countClass, unitClass } = toRefs(props);

const title = computed(() => {
  return list.value.length === 1 ? `${list.value?.[0]?.name ?? ""} ${props.title}` : props.title;
});

// 容器布局占位
const gridSpan = computed(() => {
  const colMap = {
    1: 1,
    2: 2,
    3: 2,
  };
  return {
    row: 1,
    col: list.value.length >= 4 ? (isMobile.value ? 2 : 4) : (colMap?.[list.value.length] ?? 1),
  };
});

const colStyle = computed(() => {
  return {
    "grid-template-columns": `repeat(${list.value.length}, minmax(${list.value.length === 3 || isMobile.value ? "50%" : "25%"}, 1fr))`,
  };
});

const selfCountClass = computed(() => {
  if (list.value.length === 1) {
    return ["text-[18px]", "leading-[18px]"];
  }
  return ["text-[16px]", "leading-[16px]"];
});

const showIcon = computed(() => {
  return list.value.length > 1;
});

const showUpdateTime = computed(() => {
  return list.value.length > 1 && updateTime.value;
});
</script>

<template>
  <DmpFlexCard :title="title" :grid-span="gridSpan" :show-arrow="showArrow">
    <div class="flex flex-col h-full relative">
      <DmpAutoScroller horizontal>
        <div class="grid" :style="colStyle">
          <template v-if="list.length === 1">
            <template v-for="item in list" :key="item.name">
              <DmpPopoverTrigger :id="`${id}.${item.name}`">
                <div
                  class="flex flex-col relative rounded-[12px] cursor-pointer group hover:bg-[rgba(255,255,255,0.64)] hover:shadow-[0_2px_4px_0_rgba(0,0,0,0.04)] duration-150 ease-linear"
                >
                  <div
                    class="flex flex-col gap-[2px] p-[8px]"
                    v-for="countUnitItem in item.countUnitList"
                    :key="countUnitItem.extra"
                  >
                    <div class="text-[12px] leading-[16px] text-[rgba(28,28,30,.64)]">
                      {{ countUnitItem.extra }}
                    </div>
                    <DmpValueWithUnit
                      :value="countUnitItem.count"
                      :unit="countUnitItem.unit"
                      :value-class="[...selfCountClass, ...countClass]"
                      :unit-class="unitClass"
                    />
                  </div>
                  <div
                    class="absolute top-[50%] left-[8px] right-[8px] h-[1px] bg-[rgba(0,0,0,0.04)] group-hover:opacity-[0] duration-150 ease-linear"
                  ></div>
                </div>
              </DmpPopoverTrigger>
            </template>
          </template>
          <template v-else>
            <template v-for="item in list" :key="item.name">
              <DmpPopoverTrigger :id="`${id}.${item.name}`">
                <div
                  class="flex flex-col gap-[12px] vertical-line relative p-[8px] rounded-[12px] highlightable"
                >
                  <div
                    class="flex items-center justify-center gap-[2px] text-[12px] leading-[16px] text-[#1C1C1E]"
                  >
                    <div v-if="showIcon" class="w-[16px] h-[16px]">
                      <img
                        v-if="item.icon"
                        :src="item.icon"
                        class="w-full h-full"
                        alt=""
                        srcset=""
                      />
                    </div>
                    {{ item.name }}
                  </div>
                  <div class="grid grid-cols-2 gap-[4px]">
                    <div
                      class="flex flex-col gap-[4px] items-center"
                      v-for="countUnitItem in item.countUnitList"
                      :key="countUnitItem.extra"
                    >
                      <DmpValueWithUnit
                        :value="countUnitItem.count"
                        :unit="countUnitItem.unit"
                        :value-class="[...selfCountClass, ...countClass]"
                        :unit-class="unitClass"
                      />
                      <div class="text-[12px] leading-[16px] text-[rgba(28,28,30,.64)]">
                        {{ countUnitItem.extra }}
                      </div>
                    </div>
                  </div>
                </div>
              </DmpPopoverTrigger>
            </template>
          </template>
        </div>
        <template #indicator="{ atStart, atEnd, scrollable }">
          <div
            v-if="scrollable"
            class="scroll-indicator absolute box-border bottom-[4px] w-[48px] right-[8px] px-[11px] h-[16px] rounded-[8px] bg-[rgba(28,28,30,.07)] flex items-center justify-between gap-[6px]"
            :class="{ 'at-end': atEnd || !atStart }"
          >
            <div
              class="h-[4px] rounded-full bg-[rgba(28,28,30,0.4)] transition-all duration-200 ease-linear"
              v-for="item in 2"
              :key="item"
            ></div>
          </div>
        </template>
      </DmpAutoScroller>

      <div
        v-if="showUpdateTime"
        class="mt-auto py-[4px] px-[8px] text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
      >
        {{ updateTime }}
      </div>
    </div>
  </DmpFlexCard>
</template>
