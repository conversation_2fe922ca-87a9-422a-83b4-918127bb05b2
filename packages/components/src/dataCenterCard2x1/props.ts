export interface DmpDataCenterCard2x1Props {
  id: string;
  title: string;
  updateTime: string;
  showArrow?: boolean;
  countClass?: string[];
  unitClass?: string[];
  isMobile?: boolean;
  list: DmpDataCenterCard2x1ListItem[];
}

export interface DmpDataCenterCard2x1ListItem {
  name: string;
  countUnitList: {
    count: number;
    unit: string;
    extra: string;
  }[];
  icon: string;
}

export type DmpDataCenterCard2x1Emits = {
  click: [item: DmpDataCenterCard2x1ListItem];
};
