<!--
 * @Date: 2025-07-10 13:20:48
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-18 17:17:46
 * @FilePath: /MyBusinessV2/packages/components/src/detailTable/DetailTable.vue
-->
<script setup lang="ts">
import { defineProps } from "vue";
import { DetailTableProps, DetailTableRow } from "@/detailTable/props";
import { ElTable, ElTableColumn } from "element-plus";
import { formatNumber } from "../../../common/utils/number.ts";
defineOptions({ name: "DmpDetailTable" });

const props = defineProps<DetailTableProps>();

const unitMap = props.unitMap || {};

const getHighlight = (row: DetailTableRow, col: { label: string; prop: string }) => {
  if (typeof props.highlightRule === "function") {
    return props.highlightRule(row, col);
  }
  // 默认：如果 row.highlight 为 true，则高亮
  return !!row.highlight;
};
</script>

<template>
  <div class="dmp-detail-table">
    <div class="font-medium text-[13px] text-[#1c1c1e] mb-2 leading-[20px]" v-if="props.title">
      {{ props.title }}
    </div>
    <el-table :data="props.data">
      <el-table-column
        v-for="(col, idx) in props.columns"
        :key="col.prop"
        :prop="col.prop"
        :label="col.label"
        :width="idx === 0 ? 170 : 70"
      >
        <template #default="scope">
          <slot :name="col.prop" :row="scope.row" :index="scope.$index">
            <span
              :class="[
                'text-[13px]',
                'text-[#1C1C1E]',
                'leading-[16px]',
                { 'font-medium': idx !== 0 },
                { 'highlight text-[#F63F54]': getHighlight(scope.row, col) },
              ]"
            >
              {{ formatNumber(scope.row[col.prop]) }}
            </span>
            <span
              v-if="scope.row[col.prop] != null && scope.row[col.prop] !== ''"
              :class="[
                'text-[10px]',
                'text-[#000000]',
                'leading-[12px]',
                { 'highlight text-[#F63F54]': getHighlight(scope.row, col) },
              ]"
            >
              {{ unitMap[col.prop] }}
            </span>
          </slot>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
