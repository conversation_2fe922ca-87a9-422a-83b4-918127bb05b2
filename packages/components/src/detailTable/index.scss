.dmp-detail-table {
  .el-table {
    background: rgba(28, 28, 30, 0.06) !important;
    border-radius: 12px;
    width: 100%;
    .el-table__inner-wrapper {
      padding-bottom: 12px;
    }
  }
  .el-table tr,
  .el-table th,
  .el-table td {
    background: transparent !important;
  }
  .el-table td {
    border: none !important;
    color: #1c1c1e !important;
    font-size: 11px !important;
    height: 16px !important;
    line-height: 16px !important;
    padding: 0 !important;
    padding-top: 8px !important;
    vertical-align: bottom !important;
    .cell {
      display: flex;
      align-items: flex-end;
      height: 100%;
      line-height: 16px;
    }
  }
  .el-table th {
    color: #1c1c1e !important;
    font-size: 11px !important;
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 !important;
    font-weight: normal !important;
  }
  .el-table__header {
    width: 100% !important;
    position: relative;
    &::after {
      content: "";
      display: block;
      position: absolute;
      left: 12px;
      right: 12px;
      bottom: 0;
      height: 1px;
      background: rgba(28, 28, 30, 0.08);
      z-index: 1;
    }
  }
  .el-scrollbar__view {
    width: 100% !important;
  }
  .el-table__body {
    width: 100% !important;
  }
  .hightlight {
    color: #f63f54 !important;
  }
}
