<script setup lang="ts">
import { EmptyProps } from "@/empty/props";
import defaultNoDataImage from "./assets/nodata.png";

defineOptions({ name: "DmpEmpty" });

const { image = defaultNoDataImage, text = "暂无数据" } = defineProps<EmptyProps>();
</script>

<template>
  <div class="h-full w-full flex flex-col items-center justify-center gap-[4px] p-[4px]">
    <div class="flex flex-col items-center justify-start gap-[8px]">
      <slot name="image">
        <div class="empty-image flex-shrink-0">
          <img :src="image" alt="nodata" class="w-[67px] object-contain" />
        </div>
      </slot>

      <slot name="text">
        <div class="empty-text text-center">
          <p class="text-[12px] text-[#6E6E73] text-normal">{{ text }}</p>
        </div>
      </slot>
    </div>
  </div>
</template>

<style scoped></style>
