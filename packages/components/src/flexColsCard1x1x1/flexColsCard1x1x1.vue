<script setup lang="ts">
import { FlexColsCard1x1x1Props } from "./props.ts";
import { computed, watch } from "vue";
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpAutoScroller from "@/autoScroller/autoScroller.vue";
defineOptions({ name: "DmpFlexColsCard1x1x1" })
const props = defineProps<FlexColsCard1x1x1Props>();
const {
  title,
  isMobile,
  data = []
} = props

const isCompact = computed(() => props.data.length <= 2)

const gridSpan = computed(() => {
  if (isCompact.value) return { row: 1, col: 1 };
  return { row: 1, col: 2 }
})
</script>

<template>
  <dmp-flex-card :title :grid-span>
    <dmp-auto-scroller ref="autoScroller">
      <div class="flex items-center h-full">
        <div
          v-for="(item, itemIndex) in data"
          :key="itemIndex"
          class="flex-1 min-w-[70px] flex items-center justify-center"
        >
          <div
            :class="[
                'flex flex-col gap-[8px] items-center justify-center w-full',
                itemIndex !== data.length - 1 ? 'has-border-right' : '',
              ]"
          >
            <div class="w-[20px] h-[20px] bg-[#1c1c1e] rounded-full"></div>

            <div class="flex flex-col items-center justify-center gap-[4px]">
              <div
                class="text-[18px] flex items-end text-[#1c1c1e] font-medium"
              >
                <div
                  class="text-[18px] leading-[16px] text-[#1c1c1e] font-medium"
                >
                  {{ item.count }}
                </div>
                <div
                  class="text-[10px] text-[#1c1c1e] font-bold leading-[12px]"
                >
                  {{ item.unit }}
                </div>
              </div>

              <div class="text-[12px] text-[rgba(28,28,30,.64)]">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
        <!-- 空div占位 防止布局错乱-->
        <div v-if="!isCompact">
          <div
            v-for="n in (4 - data.length % 4) % 4"
            :key="`empty-${n}`"
            class="flex-1 min-w-[70px] h-[66px]"
          />
        </div>
      </div>
    </dmp-auto-scroller>
  </dmp-flex-card>
</template>
