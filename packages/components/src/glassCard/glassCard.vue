<script setup lang="ts">
import { computed, ref } from "vue";
import { GlassCardProps } from "@/glassCard/props.ts";
import { Icon } from "../icon/icon";
import DmpAutoScroller from "../autoScroller/autoScroller.vue";
import { ElSkeleton, ElEmpty, ElSkeletonItem } from "element-plus";

defineOptions({ name: "DmpGlassCard" });

const { title, icon, loading, gridTemplate, gridSize, contentSize, disableScrolling } =
  defineProps<GlassCardProps>();

const autoScroller = ref<InstanceType<typeof DmpAutoScroller>>();

const scrollByPage = computed(() => autoScroller.value?.scrollByPage);
</script>

<template>
  <div class="dmp-glass-card relative flex flex-col">
    <div v-if="title" class="flex items-center mb-[12px]">
      <Icon v-if="icon" :name="icon" />
      <h3 class="text-[16px] font-medium leading-[24px]">{{ title }}</h3>
    </div>
    <el-skeleton animated class="h-full" :loading>
      <template #template>
        <div class="flex flex-row h-full">
          <el-skeleton-item
            v-for="item in gridTemplate.col"
            variant="rect"
            :class="`w-[calc(${Math.floor(100 / gridTemplate.col)}%-4px)] h-full min-h-[152px] rounded-[24px] mr-[8px]`"
          />
        </div>
      </template>
      <template #default>
        <dmp-auto-scroller
          ref="autoScroller"
          class="grow"
          :disabled="disableScrolling"
          :width="contentSize.width"
          :height="contentSize.height"
        >
          <template #indicator="{ atStart, atEnd, scrollable }">
            <transition name="fade">
              <div
                v-if="scrollable && scrollByPage"
                class="absolute top-[20px] right-[24px] flex gap-[2px] z-10"
              >
                <button
                  @click="() => scrollByPage(-1)"
                  :disabled="atStart"
                  class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
                  aria-label="向左滚动"
                >
                  <svg
                    class="w-[12px] h-[12px]"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  @click="() => scrollByPage(1)"
                  :disabled="atEnd"
                  class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
                  aria-label="向右滚动"
                >
                  <svg
                    class="w-[12px] h-[12px]"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </transition>
          </template>
          <template #default>
            <div
              class="grid gap-[8px] grid-flow-row"
              :style="{
                gridTemplateColumns: `repeat(${gridTemplate.col},${gridSize.col}px)`,
                'grid-template-rows': `repeat(${gridTemplate.row || 1},${gridSize.row}px)`,
                'grid-auto-rows': `${gridSize.row}px`,
              }"
            >
              <slot>
                <el-empty class="absolute-center" description="No Data" :image-size="100" />
              </slot>
            </div>
          </template>
        </dmp-auto-scroller>
      </template>
    </el-skeleton>
  </div>
</template>
