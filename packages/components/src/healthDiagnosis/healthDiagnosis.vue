<script setup lang="ts">
import { computed } from "vue";
import { HealthDiagnosisProps } from "./props";
import { useHealthDiagnosis } from "./useHealthDiagnosis";
import { DmpFlexCard } from "../flexCard";
import DmpSystemHealthIndicator from "@/systemHealthIndicator/systemHealthIndicator.vue";
import DmpTipCarousel from "@/tipCarousel/tipCarousel.vue";
import DmpAlertStatusList from "@/alertStatusList/alertStatusList.vue";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";

defineOptions({
  name: "DmpHealthDiagnosis",
});

const props = withDefaults(defineProps<HealthDiagnosisProps>(), {
  id: "",
  progress: 77,
  statusText: "健康度",
  openDirection: "top",
  size: 90,
  strokeWidth: 10,
  strokeColor: () => ["#2EBB72", "#9DE76A"],
  interval: 3000,
  loop: true,
  autoplay: true,
  isMobile: false,
  title: "健康诊断",
  icon: "",
  level: "normal",
});

const { colorMap, defaultHealthTips, iconMap } = useHealthDiagnosis();

const actualHealthTips = computed(() => {
  return props.healthTips && props.healthTips.length > 0
    ? props.healthTips
    : defaultHealthTips.value;
});

const actualAlertItems = computed(() => {
  return props.alertItems && props.alertItems.length > 0 ? props.alertItems : [];
});

const indicatorSize = computed(() => {
  return props.isMobile ? 90 : props.size;
});

const currentLevelIcon = computed(() => {
  return iconMap[props.level as keyof typeof iconMap];
});

const currentLevelColor = computed(() => {
  return colorMap[props.level as keyof typeof colorMap];
});
</script>

<template>
  <div class="dmp-health-diagnosis">
    <div class="health-content h-full">
      <DmpFlexCard :showArrow="false" class="!p-[16px] !h-[152px]">
        <DmpPopoverTrigger id="healthDiagnosis">
          <div class="progress-section cursor-pointer highlightable">
            <div class="flex items-center gap-3">
              <div class="relative">
                <DmpSystemHealthIndicator
                  :progress="progress"
                  :statusText="statusText"
                  :openDirection="openDirection"
                  :size="indicatorSize"
                  :strokeWidth="strokeWidth"
                  :strokeColor="currentLevelColor"
                  :level="level"
                />

                <div class="level-icon absolute bottom-[5px] left-[50%] translate-x-[-50%]">
                  <img
                    :src="currentLevelIcon"
                    :alt="`${props.level || 'normal'} level`"
                    class="w-[22px] h-[22px] object-contain"
                  />
                </div>
              </div>
            </div>
            <DmpTipCarousel
              :tips="actualHealthTips"
              :interval="interval"
              :loop="loop"
              :autoplay="autoplay"
            />
          </div>
        </DmpPopoverTrigger>
      </DmpFlexCard>

      <DmpFlexCard :showArrow="false" class="flex-1">
        <DmpAlertStatusList :items="actualAlertItems" />
      </DmpFlexCard>
    </div>
  </div>
</template>
