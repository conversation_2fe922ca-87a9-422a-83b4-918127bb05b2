import { ref, nextTick } from "vue";
import { AlertStatusItem } from "./props";

// 导入图片资源
import normalIcon from "./assets/normal.png";
import warningIcon from "./assets/warning.png";
import criticalIcon from "./assets/good.png";

export const useHealthDiagnosis = () => {
  // 根据 level 映射对应的图片
  const iconMap = {
    normal: normalIcon,
    warning: warningIcon,
    good: criticalIcon,
  };

  const colorMap = {
    warning: ["#FF8175", "#F63F54"],
    normal: ["#FFE167", "#FF9500"],
    good: ["#2EBB72", "#9DE76A"],
  };

  const defaultHealthTips = ref<string[]>([
    "您的系统出现多个预警信号，建议立即检查！",
    "请检查您的系统，确保其正常运行。",
    "最近系统运行状态良好，请继续保持。",
  ]);

  // 计算圆点位置
  function updateDotPositions() {
    nextTick(() => {
      const progressBars = document.querySelectorAll(".progress-bar");
      progressBars.forEach((progressBar: Element) => {
        const progressOuter = progressBar.querySelector(".el-progress-bar__outer") as HTMLElement;
        const progressInner = progressBar.querySelector(".el-progress-bar__inner") as HTMLElement;
        const dot = progressBar.querySelector(".custom-dot") as HTMLElement;

        if (progressOuter && progressInner && dot) {
          // 获取外层容器的宽度
          const outerWidth = progressOuter.offsetWidth;
          // 从 data-percentage 属性获取实际的进度百分比
          const percentage = parseInt((progressBar as HTMLElement).dataset.percentage || "0");
          // 计算圆点位置
          const dotPosition = (outerWidth * percentage) / 100;
          dot.style.left = `${dotPosition}px`;
        }
      });
    });
  }

  return {
    colorMap,

    defaultHealthTips,
    updateDotPositions,
    iconMap,
  };
};
