import { FunctionalComponent, h } from "vue";
import { defineAsyncComponent } from "vue";

interface FComponentProps {
  name: string;
  // Other props, such as width, color, etc.
}

export const Icon: FunctionalComponent<FComponentProps> = (props) => {
  const AsyncComponent = defineAsyncComponent(
    () => import(`@assets/icons/${props.name}.svg?component`),
  );

  return h(AsyncComponent, {});
};

Icon.props = {
  name: {
    type: String,
    required: true,
  },
};
