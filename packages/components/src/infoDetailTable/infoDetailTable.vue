<!--
 * @Date: 2025-07-11 16:56:26
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-17 11:15:06
 * @FilePath: /MyBusinessV2/packages/components/src/infoDetailTable/infoDetailTable.vue
-->
<template>
  <div>
    <div v-if="title" class="mb-2 text-[13px] font-medium text-[#1C1C1E] leading-[20px]">
      {{ title }}
    </div>
    <div class="px-3 py-1 rounded-[12px] bg-[rgba(28,28,30,0.06)] backdrop-blur-[12px]">
      <div
        v-for="(item, idx) in items"
        :key="item.name"
        class="flex justify-between items-center py-2 leading-[16px] text-[#1C1C1E] border-b border-[#E5E6EB] last:border-b-0"
      >
        <span class="font-normal text-[12px]">{{ item.name }}</span>
        <div>
          <span class="font-bold text-[13px]">{{ item.value }}</span>
          <span v-if="item.unit" class="font-medium text-[10px] leading-[12px] text-[#000000]">{{
            item.unit
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoDetailItem } from "@/infoDetailTable/props";
defineProps<{
  items: InfoDetailItem[];
  title?: string;
}>();
</script>
