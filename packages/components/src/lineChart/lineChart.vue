<script setup lang="ts">
import { computed } from "vue";
import * as echarts from "echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { LineChartProps } from "@/lineChart/props";
use([Canvas<PERSON><PERSON>er, LineChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);
defineOptions({ name: "Dmp<PERSON>ine<PERSON><PERSON>" });

const props = defineProps<LineChartProps>();

const finalOption = computed(() => ({
  grid: {
    left: 8,
    right: 8,
    top: 8,
    bottom: 8,
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "line" },
    backgroundColor: "rgba(60,60,60,0.9)",
    borderRadius: 6,
    padding: 8,
    textStyle: { color: "#fff", fontSize: 12 },
  },
  legend: { show: false },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: props.xAxisData,
    axisLine: {
      show: true,
      lineStyle: {
        color: "#E5E5EA",
        width: 3,
        cap: "round", // 两端圆角（echarts 5.5+ 支持）
      },
      z: 10,
    },
    axisTick: {
      show: true,
      alignWithLabel: true,
      length: 8,
      lineStyle: {
        color: "#E5E5EA",
        width: 3,
        cap: "round",
      },
    },
    axisLabel: {
      color: "#AEAEB2",
      fontSize: 11,
      fontWeight: 400,
      margin: 10,
      align: "center",
      padding: [0, 0, 0, 0],
    },
    splitLine: { show: false },
  },
  yAxis: {
    type: "value",
    min: props.yMin ?? 0,
    max: props.yMax ?? 1000,
    interval: props.yInterval ?? 200,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: "#AEAEB2",
      fontSize: 11,
      margin: 2,
      align: "right",
      padding: [0, 4, 0, 0],
      formatter: function (val: number) {
        return val.toLocaleString();
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "#E5E6EB",
        type: "dashed",
        width: 1,
      },
    },
  },
  series: props.series.map((s) => ({
    type: "line",
    symbol: "circle",
    symbolSize: 6,
    itemStyle: {
      color: s.color ?? "#4CB6D8",
      borderColor: "#4CB6D8",
      borderWidth: 2,
      //   shadowColor: "rgba(76,182,216,0.2)",
    },
    lineStyle: {
      color: s.color ?? "#4CB6D8",
      width: 2,
    },
    areaStyle: {
      color: "rgba(87,173,196,0.12)",
    },
    emphasis: {
      focus: "series",
      itemStyle: {
        borderColor: s.color ?? "#4CB6D8",
        borderWidth: 4,
      },
    },
    smooth: false,
    ...s,
  })),
}));
</script>

<template>
  <div>
    <div class="text-[12px] font-medium text-[#1C1C1E] leading-[20px] mb-2">
      {{ props.title }}
    </div>
    <div class="flex items-center justify-between mb-3">
      <div class="text-[11px] font-normal text-[#1C1C1E] leading-[16px]">
        {{ props.subtitle }}
      </div>
      <!-- <div v-if="props.series?.length" class="flex items-center">
        <span
          class="inline-block w-2 h-2 rounded-full mr-1"
          :style="{ background: props.series[0].color ?? '#59ADC4' }"
        ></span>
        <span class="text-[11px] font-normal text-[#1C1C1E] leading-[16px]">
          {{ props.series[0].name }}
        </span>
      </div> -->
    </div>
    <v-chart :option="finalOption" autoresize style="height: 174px; width: 100%" />
  </div>
</template>
