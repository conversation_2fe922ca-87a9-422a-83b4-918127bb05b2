@media (max-width: 768px) {
  .dmp-popover {
    position: fixed !important;
    width: 100% !important;
    height: auto !important;
    inset: auto auto 0 0 !important;
    border-radius: 24px 24px 0 0 !important;
    padding: 20px !important;
    max-height: 800px !important;
    overflow-y: auto !important;
    overflow-x: hidden;
  }
}

@media (min-width: 768px) {
  .dmp-popover {
    border-radius: 24px !important;
  }
}

.dmp-popover {
  border-radius: 24px !important;
  padding: 20px !important;
  background: rgba(255, 255, 255, 0.64) !important;
  box-shadow:
    0 0 8px rgba($color: #000000, $alpha: 0.08),
    0 16px 32px 0 rgba(0, 0, 0, 0.16);
  backdrop-filter: blur(16px) !important;
}
.popover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  .popover-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
  }
  .popover-header-extra {
    background: rgba(0, 113, 227, 0.2);
    cursor: pointer;
    color: #0071e3;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 16px;
  }
}
