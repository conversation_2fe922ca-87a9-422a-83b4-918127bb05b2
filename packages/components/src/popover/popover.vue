<!--
 * @Date: 2025-07-09 17:14:25
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-15 19:34:54
 * @FilePath: /MyBusinessV2/packages/components/src/popover/popover.vue
-->
<script setup lang="ts">
/**
 * 通用弹窗浮层组件，基于 Element Plus el-popover 封装。
 * 支持 placement、width、trigger、popperClass、teleported 等参数。
 * reference 和内容均用 slot，便于复用。
 */
import { defineProps, withDefaults, defineEmits } from "vue";
import { popoverProps } from "@/popover/props.ts";
import { Icon } from "../icon/icon";
defineOptions({ name: "DmpPopover" });
const props = withDefaults(defineProps<popoverProps>(), {
  title: "健康诊断",
  placement: "right",
  width: 360,
  trigger: "hover",
  popperClass: "dmp-popover",
  teleported: true,
  extraIcon: "download",
  extraTitle: "下载明细",
});
const emit = defineEmits(["extra-click", "show", "hide"]);
function handleExtraClick(e: MouseEvent) {
  emit("extra-click", e);
}
function handleShow() {
  emit("show");
}
function handleHide() {
  emit("hide");
}
</script>
<template>
  <el-popover
    :trigger="trigger as any"
    :placement="placement as any"
    :width="width"
    :show-arrow="true"
    :popper-class="popperClass"
    :teleported="teleported"
    @show="handleShow"
    @hide="handleHide"
  >
    <template #reference>
      <slot name="reference" />
    </template>
    <div class="popover-header">
      <span class="popover-header-title">{{ title }}</span>
      <div
        class="popover-header-extra flex items-center cursor-pointer"
        v-if="extraTitle"
        @click="handleExtraClick"
      >
        <Icon class="mr-[2px]" :name="extraIcon" />
        <span>{{ extraTitle }}</span>
      </div>
    </div>
    <slot />
  </el-popover>
</template>
