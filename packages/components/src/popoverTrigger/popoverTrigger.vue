<script setup lang="ts">
import { PopoverTriggerProps } from "@/popoverTrigger/props.ts";
import DmpPopover from "@/popover/popover.vue";
import { computed, inject, toRefs } from "vue";
import { CompRegistry, Schema } from "../../../common/types";
import { comparePaths, getMatches, replaceMatches } from "@/utils";

defineOptions({ name: "DmpPopoverTrigger" });
const compsRegistry = inject<CompRegistry>("compsRegistry");
const schema = inject<Schema>("schema");
const { id } = defineProps<PopoverTriggerProps>();

const foundKey = computed(() =>
  Object.keys(schema.popovers)
    .reverse()
    .find((key) => comparePaths(key, id))
);
const matches = computed(() => getMatches(foundKey?.value, id));
const popoverConfig = computed(() => schema.popovers[foundKey?.value]);
const { type, title, extraIcon, extraTitle, props } = toRefs(popoverConfig?.value);
const finalTitle = computed(() => replaceMatches(title?.value, matches?.value));
const finalExtraIcon = computed(() => replaceMatches(extraIcon?.value, matches?.value));
const finalExtraTitle = computed(() => replaceMatches(extraTitle?.value, matches?.value));
const finalProps = computed(() => props && JSON.parse(replaceMatches(JSON.stringify(props.value), matches?.value)));

const popoverCompName = computed(() => type?.value);
</script>
<template>
  <dmp-popover
    class="dmp-popover"
    :title="finalTitle"
    :extra-icon="finalExtraIcon"
    :extra-title="finalExtraTitle"
    trigger="click"
  >
    <template #reference>
      <slot />
    </template>
    <component
      v-if="compsRegistry?.[popoverCompName]"
      class="dmp-popover-trigger"
      :is="compsRegistry[popoverCompName]"
      v-bind="finalProps"
    />
  </dmp-popover>
</template>