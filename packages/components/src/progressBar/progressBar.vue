<script setup lang="ts">
import { ProgressBarProps } from "./props";
import { useProgressBar } from "./useProgressBar";
import "./index.scss";

defineOptions({
  name: "DmpProgressBar",
});

const props = withDefaults(defineProps<ProgressBarProps>(), {
  progress: 0,
  gradient: () => ["#FF8175", "#F63F54"],
  statusLevel: "normal",
  showDot: true,
  height: 4,
  borderRadius: 2,
  showText: true,
  progressUnit: "",
  warningCnt: 0,
  color: "rgba(28,28,30,0.24)",
  textColor: "rgba(28,28,30,0.56)",
});

const { trackStyle, fillStyle, shouldShowDot, progressContainerClass } = useProgressBar(props);

const { showText, progress, progressUnit, warningCnt } = props;
</script>

<template>
  <div :class="progressContainerClass">
    <div class="progress-track" :style="trackStyle">
      <div class="progress-fill" :style="fillStyle" :data-show-dot="shouldShowDot"></div>
    </div>
    <div class="progress-text" v-if="showText">
      <span :style="{ color: textColor }">{{ warningCnt }}</span>
      <span class="progress-unit" :style="{ color: textColor }">{{ progressUnit }}</span>
    </div>
  </div>
</template>
