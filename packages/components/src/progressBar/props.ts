export interface ProgressBarProps {
  /** 进度百分比 0-100 */
  progress?: number;
  /** 渐变色数组 */
  gradient?: string[];
  /** 单色 */
  color?: string;
  /** 状态级别 */
  statusLevel?: "normal" | "warning" | "good" | "qualified";
  /** 是否显示小圆点 */
  showDot?: boolean;
  /** 进度条高度 */
  height?: number;
  /** 进度条圆角 */
  borderRadius?: number;
  /** 是否显示进度文字 */
  showText?: boolean;
  /** 进度单位 */
  progressUnit?: string;
  /** 警告数量 */
  warningCnt?: number;
  /** 文字颜色 */
  textColor?: string;
}
