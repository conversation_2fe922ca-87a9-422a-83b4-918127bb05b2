import { computed } from "vue";
import { ProgressBarProps } from "./props";

export function useProgressBar(props: ProgressBarProps) {
  // 计算进度条的样式
  const progressStyle = computed(() => {
    const percentage = Math.min(Math.max(props.progress || 0, 0), 100);

    let background: string;
    let dotColor: string;

    if (props.color) {
      // 如果提供了单色，使用单色
      background = props.color;
      dotColor = props.color;
    } else if (props.gradient && props.gradient.length >= 2) {
      // 如果提供了渐变色数组，使用渐变
      background = `linear-gradient(90deg, ${props.gradient.join(", ")})`;
      dotColor = props.gradient[props.gradient.length - 1];
    } else {
      // 默认渐变色
      background = "linear-gradient(90deg, #FF8175, #F63F54)";
      dotColor = "#F63F54";
    }

    return {
      width: `${percentage}%`,
      background,
      "--dot-color": dotColor,
    };
  });

  // 计算进度条轨道样式
  const trackStyle = computed(() => {
    const height = props.height ? `${props.height}px` : "4px";
    const borderRadius = props.borderRadius ? `${props.borderRadius}px` : "2px";

    return {
      height,
      borderRadius,
    };
  });

  // 计算进度条填充样式
  const fillStyle = computed(() => {
    const borderRadius = props.borderRadius ? `${props.borderRadius}px` : "2px";

    return {
      ...progressStyle.value,
      borderRadius,
    };
  });

  // 是否显示小圆点
  const shouldShowDot = computed(() => {
    if (props.showDot === false) return false;
    return (props.progress || 0) > 0;
  });

  // 根据状态级别确定进度条容器样式
  const progressContainerClass = computed(() => {
    const baseClass = "progress-container";
    switch (props.statusLevel) {
      case "warning":
        return `${baseClass} progress-warning`;
      case "good":
        return `${baseClass} progress-good`;
      default:
        return `${baseClass} progress-normal`;
    }
  });

  return {
    progressStyle,
    trackStyle,
    fillStyle,
    shouldShowDot,
    progressContainerClass,
  };
}
