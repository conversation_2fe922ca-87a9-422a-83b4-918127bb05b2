export interface RankItem {
  id: string | number;
  title: string;
  image?: string;
  hideArrow?: boolean;
  name?: string;
  rank?: number;
  subTitle?: string;
}
export interface GridSpan {
  row: number;
  col: number;
}

export interface TabItem {
  label: string;
  value: string;
}

export interface RankCardProps {
  id: string;
  rankData: RankItem[];
  title?: string;
  gridSpan?: GridSpan;
  tabs?: TabItem[];
}
