<script setup lang="ts">
import { RankCardProps } from "./props";
import { DmpItemWithArrow } from "../itemWithArrow";
import { DmpFlexCard } from "../flexCard";
import { DmpTabSwitcher } from "../tabSwitcher";
import { DmpEmpty } from "../empty";
import { useRankCard } from "./useRankCard";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";

defineOptions({
  name: "DmpRankCard",
});

const props = withDefaults(defineProps<RankCardProps>(), {
  id: "",
  rankData: () => [],
  title: "",
  gridSpan: () => ({
    row: 1,
    col: 1,
  }),
  tabs: () => [],
});

const emit = defineEmits(["tabChange"]);

const { currentTab, currentRankData, getRankImage, handleTabChange } = useRankCard(props);

const onTabChange = (value: string) => {
  handleTabChange(value);
  emit("tabChange", value);
};
</script>

<template>
  <DmpFlexCard
    class="flex flex-col gap-[8px]"
    :gridSpan="gridSpan"
    :title="title"
    :showArrow="false"
  >
    <div class="flex flex-col h-full">
      <DmpTabSwitcher
        :tabs="tabs"
        :defaultTab="currentTab"
        equalWidth
        @change="onTabChange"
        class="!mx-[8px]"
      />

      <template v-if="currentRankData.length > 0">
        <DmpPopoverTrigger :id="`${id}.${currentTab}`">
          <div>
            <DmpItemWithArrow
              v-for="(item, index) in currentRankData"
              :key="item.id"
              class="h-[72px] px-[16px] shrink-0"
              hideArrow
            >
              <div class="flex items-center gap-[12px] w-full">
                <div class="w-[24px] shrink-0">
                  <img
                    v-if="item.rank <= 10"
                    :src="getRankImage(item.rank)"
                    :alt="`rank-${item.rank}`"
                    :class="['w-full h-full object-cover  mx-[auto]']"
                  />
                </div>
                <div class="flex-1 flex items-center gap-[12px] min-w-0">
                  <div
                    class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff] flex items-center justify-center overflow-hidden"
                  >
                    <img
                      v-if="item.image"
                      :src="item.image"
                      :alt="item.title"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  <div class="flex flex-col gap-[2px] font-medium flex-1 min-w-0">
                    <div class="text-[13px] text-[#1C1C1E] truncate">
                      {{ item.title }}
                    </div>
                    <div class="text-[13px] text-[#6E6E73] truncate">
                      {{ item.subTitle }}
                    </div>
                  </div>
                </div>
              </div>
            </DmpItemWithArrow>
          </div>
        </DmpPopoverTrigger>
      </template>
      <DmpEmpty v-else />
    </div>
  </DmpFlexCard>
</template>
