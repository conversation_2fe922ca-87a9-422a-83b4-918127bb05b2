<!--
 * @Date: 2025-07-15 17:12:06
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-17 09:31:08
 * @FilePath: /MyBusinessV2/packages/components/src/simpleDropdown/simpleDropdown.vue
-->
<script setup lang="ts">
import { ref, watch } from "vue";
import { onClickOutside } from "@vueuse/core";
import { ArrowUpBold } from "@element-plus/icons-vue";
import { SimpleDropdownProps } from "./props";

defineOptions({ name: "DmpSimpleDropdown" });

const props = defineProps<SimpleDropdownProps>();
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const showDropdown = ref(false);
const rootRef = ref<HTMLElement | null>(null);

function selectOption(val: string) {
  emit("update:modelValue", val);
  showDropdown.value = false;
}

onClickOutside(rootRef, () => {
  showDropdown.value = false;
});
</script>

<template>
  <div class="relative" ref="rootRef">
    <button
      class="px-2 py-2 rounded-[12px] bg-white flex items-center text-[12px] font-medium leading-[20px] text-[#1C1C1E]"
      @click="showDropdown = !showDropdown"
      type="button"
    >
      {{ options.find((opt) => opt.value === modelValue)?.label || modelValue }}
      <el-icon :size="14" class="ml-1"><ArrowUpBold /></el-icon>
    </button>
    <div
      v-if="showDropdown"
      class="absolute z-20 mt-1 w-27 rounded-[12px] px-[2px] py-[2px] bg-white shadow-[0_16px_32px_0_rgba(0,0,0,0.16)] backdrop-blur-sm z-[99]"
    >
      <div
        v-for="opt in options"
        :key="opt.label + opt.value"
        class="px-4 py-2 rounded-[10px] hover:bg-gray-100 cursor-pointer text-[12px] mt-[2px] first:mt-0"
        :class="{
          ' text-[#0071e3] bg-[#0071e3]/[0.08] font-medium': opt.value === modelValue,
        }"
        @click="selectOption(opt.value)"
      >
        {{ opt.label }}
      </div>
    </div>
  </div>
</template>
