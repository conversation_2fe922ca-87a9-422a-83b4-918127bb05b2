@import "sidebar/index.scss";
@import "sidebarMobile/index.scss";
@import "popover/index.scss";
@import "glassCard/index.scss";
@import "flexCard/index.scss";
@import "flexColsCard1x1x1/index.scss";
@import "detailTable/index.scss";
@import "barChart/index.scss";
@import "cycleSelector/index.scss";
@import "infoDetailTable/index.scss";
@import "lineChart/index.scss";
@import "barStackChart/index.scss";
@import "dataCenterCard1x1/index.scss";
@import "valueWithUnit/index.scss";
@import "autoScroller/index.scss";
@import "dataCenterCard2x1/index.scss";
@import "popoverTrigger/index.scss";
@import "popoverProvider/index.scss";
@import "tailwind.css";
@import "circleProgress/index.scss";
@import "tailwind.css";
@import "systemHealthIndicator/index.scss";
@import "tipCarousel/index.scss";
@import "alertStatusList/index.scss";
@import "alertStatusItem/index.scss";
@import "healthDiagnosis/index.scss";
@import "progressBar/index.scss";
@import "tabSwitcher/index.scss";
@import "itemWithArrow/index.scss";
@import "popoverRank/index.scss";
@import "rankCard/index.scss";
@import "simpleDropdown/index.scss";
@import "empty/index.scss";
@import "healthDiagnosis/index.scss";
@import "textList/index.scss";

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.highlightable {
  cursor: pointer;
  transition: all 150ms linear;

  &:hover {
    background: rgba(255, 255, 255, 0.64);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.04);
  }
}
