<script setup lang="ts">
import DmpCircleProgress from "@/circleProgress/circleProgress.vue";
import { SystemHealthIndicatorProps } from "./props";

const props = withDefaults(defineProps<SystemHealthIndicatorProps>(), {
  progress: 0,
  statusText: "健康度",
  openDirection: "bottom",
  size: 90,
  strokeWidth: 10,
  strokeColor: () => ["#2EB972", "#9DE76A"],
});
</script>

<template>
  <div class="system-health-indicator">
    <DmpCircleProgress
      :progress="props.progress"
      :label="props.statusText"
      :open-direction="props.openDirection"
      :size="props.size"
      :stroke-width="props.strokeWidth"
      :stroke-color="props.strokeColor"
    />
  </div>
</template>
