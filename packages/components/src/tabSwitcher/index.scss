.tab-switcher-wrap.big-large {
  height: 56px;
  border-radius: 28px;
}
.tabs-bg.big-large {
  height: 56px;
  border-radius: 28px;
}
.tabs-bg {
  background-color: rgba(229, 229, 234, 0.5);
}
.disabled-tab {
  cursor: not-allowed;
  opacity: 0.6;
}
.big-large {
  border-radius: 28px;
  .mask-box {
    border-radius: 24px;
    height: 48px;
  }
}
.large {
  border-radius: 20px;
  .mask-box {
    border-radius: 16px;
  }
}

.middle {
  border-radius: 8px;
  .mask-box {
    border-radius: 6px;
  }
}

.right-line:not(:last-child) {
  &::after {
    content: "";
    position: absolute;
    right: 0px;
    top: 6px;
    width: 1px;
    height: 20px;
    background-color: #e5e5ea;
  }
}