<script setup lang="ts">
import { TabSwitcherProps } from "./props";
import { useTabSwitcher } from "./useTabSwitcher";

const props = withDefaults(defineProps<TabSwitcherProps>(), {
  animation: true,
  tabs: () => [
    { label: "Tab 1", value: "tab1" },
    { label: "Tab 2", value: "tab2" },
    { label: "Tab 3", value: "tab3" },
  ],
  equalWidth: true,
  disabled: false,
  bgColor: "#E5E5EA",
  defaultTab: null,
  disabledTabs: () => [],
  mode: "large",
  showDivider: false,
  btnClass: "",
  tabHeight: 40,
  defaultPadding: 4,
  slideClass: "",
});

const emit = defineEmits(["change", "update:defaultTab"]);

const {
  tabButtons,
  tabsWrapper,
  activeTab,
  changeTab,
  updateSlidePosition,
  slideTargetWidth,
  translateX,
} = useTabSwitcher(props, emit);
</script>

<template>
  <div
    class="tab-switcher-wrap relative h-[40px]"
    :class="{
      [props.mode]: true,
      'pointer-events-none opacity-50': props.disabled,
    }"
  >
    <div
      class="relative w-full h-[40px] tabs-bg"
      :class="[props.mode]"
      :style="{ backgroundColor: props.bgColor }"
    >
      <!-- Tab buttons -->
      <div
        class="relative w-full h-full flex items-center z-10 p-[4px]"
        :ref="(el) => (tabsWrapper = el as HTMLElement)"
      >
        <div
          v-for="(tab, index) in props.tabs"
          :key="index"
          class="flex items-center"
          :ref="(el) => (tabButtons[index] = el as HTMLElement)"
          :class="[props.equalWidth ? 'flex-1' : '']"
        >
          <slot
            name="btns"
            :tab="tab"
            :activeTab="activeTab"
            :index="index"
            :key="tab.value ?? index"
          >
            <button
              class="flex-1 text-[13px] text-center transition-all px-[16px] duration-200 text-#6E6E73 h-[32px] p-[6px] overflow-hidden whitespace-nowrap text-ellipsis"
              :class="[
                activeTab === tab.value ? 'font-medium text-#1C1C1E' : 'font-normal',
                props.disabledTabs.includes(tab.value) ? 'disabled-tab' : '',
                btnClass,
              ]"
              @click="changeTab(tab.value, index)"
            >
              <slot name="tab" :tab="tab" :index="index">
                {{ tab.label }}
              </slot>
            </button>
            <div
              class="line flex-0-auto w-[1px] h-[20px] bg-#E5E5EA"
              v-if="props.showDivider && index + 1 !== props.tabs.length"
            ></div>
          </slot>
        </div>
      </div>

      <!-- Sliding indicator -->
      <div
        :class="[
          'absolute h-[32px] bg-white shadow-box mask-box',
          animation ? 'transition-all duration-200 ease-in-out' : '',
          slideClass,
        ]"
        :style="{
          top: '50%',
          width: `${slideTargetWidth}px`,
          transform: `translateY(-50%) translateX(${translateX}px)`,
        }"
      ></div>
    </div>
  </div>
</template>
