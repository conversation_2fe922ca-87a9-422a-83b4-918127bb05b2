import { ref, onMounted, nextTick, watch, onUnmounted } from "vue";
import { TabSwitcherProps } from "./props";

export const useTabSwitcher = (props: TabSwitcherProps, emit: any) => {
  const tabButtons = ref<HTMLElement[]>([]);
  const tabsWrapper = ref<HTMLElement | null>(null);
  const activeTab = ref(props.tabs[0]?.value || "");
  const activeTabIndex = ref(0);
  const slideTargetWidth = ref(0);
  const translateX = ref(0);

  const changeTab = (value: string, index: number) => {
    if (props.disabledTabs.includes(value)) {
      return;
    }
    activeTab.value = value;
    activeTabIndex.value = index;
    emit("change", value, index);
    emit("update:defaultTab", value);
    updateSlidePosition();
  };

  // 更新 translateX 的函数
  const updateSlidePosition = () => {
    const currentTab = tabButtons.value[activeTabIndex.value];
    if (currentTab) {
      const { left } = currentTab.getBoundingClientRect();
      const width = currentTab.offsetWidth;
      const { left: wrapperLeft } = tabsWrapper.value.getBoundingClientRect();
      const _left = (wrapperLeft - left).toFixed(2);
      translateX.value = -_left;
      slideTargetWidth.value = props.showDivider ? width - 1 : width;
    }
  };

  const observer = new ResizeObserver(updateSlidePosition);

  onMounted(async () => {
    nextTick(() => {
      activeTab.value = props.defaultTab || props.tabs[0]?.value;
      activeTabIndex.value = props.defaultTab
        ? props.tabs.findIndex((tab) => tab.value === props.defaultTab)
        : 0;
      tabButtons.value.forEach((button) => observer.observe(button));
      updateSlidePosition();
    });
  });

  const reset = () => {
    activeTab.value = props.tabs[0]?.value;
    activeTabIndex.value = 0;
    updateSlidePosition();
  };

  onUnmounted(() => {
    observer.disconnect();
  });

  watch(
    () => props.defaultTab,
    (val) => {
      nextTick(() => {
        if (val) {
          activeTab.value = props.defaultTab || props.tabs[0]?.value;
          activeTabIndex.value = props.defaultTab
            ? props.tabs.findIndex((tab) => tab.value === props.defaultTab)
            : 0;
          updateSlidePosition();
        }
      });
    },
    { deep: true }
  );

  return {
    tabButtons,
    tabsWrapper,
    activeTab,
    activeTabIndex,
    slideTargetWidth,
    translateX,
    changeTab,
    updateSlidePosition,
    reset,
  };
};
