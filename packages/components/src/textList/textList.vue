<script setup lang="ts">
import { computed } from "vue";
import { TextListProps } from "./props";

defineOptions({ name: "DmpTextList" });

const {
  texts = [],
  textSize = "text-[10px]",
  textColor = "text-[#6E6E73]",
  gap = "gap-[4px]",
} = defineProps<TextListProps>();

// 检查和过滤有效的文本
const validTexts = computed(() => {
  if (!texts || !Array.isArray(texts)) {
    return [];
  }
  return texts.filter((text) => text != null && text !== undefined && text !== "");
});
</script>

<template>
  <div class="dmp-text-list flex flex-col" :class="gap" v-if="validTexts.length > 0">
    <p v-for="(text, index) in validTexts" :key="index" :class="[textSize, textColor]">
      {{ text }}
    </p>
  </div>
</template>

<style scoped>
@import "./index.scss";
</style>
