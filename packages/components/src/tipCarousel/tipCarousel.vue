<script setup lang="ts">
import { useTipCarousel } from "./useTipCarousel";
import { TipCarouselProps } from "./props";

const props = withDefaults(defineProps<TipCarouselProps>(), {
  tips: () => [],
  interval: 3000,
  loop: true,
  autoplay: true,
});

const { currentIndex } = useTipCarousel(props);
</script>
<template>
  <div
    class="font-medium leading-[16px] text-[11px] text-center overflow-hidden"
  >
    <p :key="`tip-${currentIndex}-${props.tips[currentIndex]}`">
      {{ props.tips[currentIndex] }}
    </p>
  </div>
</template>
