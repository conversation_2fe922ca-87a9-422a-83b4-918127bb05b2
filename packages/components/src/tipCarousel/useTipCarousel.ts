import { ref, onMounted, onBeforeUnmount } from "vue";

export const useTipCarousel = (props) => {
  const currentIndex = ref(0);
  const frameId = ref<number | null>(null);
  const lastTime = ref<number>(performance.now());

  const loop = (now: number) => {
    if (props.tips.length <= 1) return;

    const elapsed = now - lastTime.value;

    if (elapsed >= props.interval) {
      currentIndex.value = (currentIndex.value + 1) % props.tips.length;
      lastTime.value = now;
    }

    frameId.value = requestAnimationFrame(loop);
  };

  const start = () => {
    lastTime.value = performance.now();
    frameId.value = requestAnimationFrame(loop);
  };

  const stop = () => {
    if (frameId.value !== null) {
      cancelAnimationFrame(frameId.value);
      frameId.value = null;
    }
  };

  onMounted(start);
  onBeforeUnmount(stop);

  return {
    currentIndex,
    start,
    stop,
  };
};
