export function dfs<T extends { children?: T[] }>(node: T, cb: (n: T) => void) {
  cb(node);
  node.children && node.children.forEach((child) => dfs(child, cb));
}

export function comparePaths(path1: string, path2: string) {
  const path1List = path1.split('.')
  const path2List = path2.split('.')
  if (path1List.length !== path2List.length) return false

  for (let i = 0; i < path1List.length; i++) {
    const item1 = path1List[i]
    const item2 = path2List[i]
    if (item1 !== '*' && item2 !== '*' && item1 !== item2) {
      return false
    }
  }

  return true
}

export function getMatches(path1: string, path2: string) {
  const matches: string[] = []
  if (!path1 || !path2) return matches

  const path1List = path1.split('.')
  const path2List = path2.split('.')

  for (let i = 0; i < path1List.length; i++) {
    const item1 = path1List[i]
    const item2 = path2List[i]
    if (item1 === '*') {
      matches.push(item2)
    }
  }

  return matches
}

export function replaceMatches(source: string, matches: string[]) {
  if (!source) return source
  for (let i = 0; i < matches.length; i++) {
    const regExp = new RegExp(`\\$${i + 1}`, 'g')
    console.log('replace', regExp, matches[i])
    source = source.replace(regExp, matches[i])
  }
  return source
}
