<script setup lang="ts">
import { toRefs, withDefaults } from "vue";
import { DmpValueWithUnitProps } from "./props.ts";

defineOptions({ name: "DmpValueWithUnit" });
const props = withDefaults(defineProps<DmpValueWithUnitProps>(), {
  valueClass: () => [],
  unitClass: () => [],
});
const { value, unit, valueClass, unitClass } = toRefs(props);
</script>

<template>
  <div class="flex items-end gap-[2px] text-[#1C1C1E]">
    <div class="font-medium" :class="['text-[16px]', 'leading-[16px]', ...valueClass]">
      {{ value }}
    </div>
    <div class="font-medium" :class="['text-[10px]', 'leading-[12px]', ...unitClass]">
      {{ unit }}
    </div>
  </div>
</template>
