schemaVersion: 2.0
timeout: 20
secrets:
  names:
    - dmp_eng_token
# For reasoning behind freestyle template, please see the following:
# - https://docs.pie.apple.com/artifactory/whats-new.html#permission-changes-in-docker-registry
# - https://stackoverflow.apple.com/questions/13373/how-do-i-fix-the-dockerfilev1publish-extra-tags-build-error

pipelines:
  - machine:
      baseImage: docker.apple.com/pie/fuji:latest
    branchName: dev
    build:
      template: freestyle:v4:publish
      steps:
        - "true"
    package:
      dockerfile:
        - dockerfilePath: Dockerfile
          env:
            BASE_VERSION: "dev_1.0.0.${RIO_BUILD_NUMBER}"
            BASE_ENV: "dev"
          version: next
          extraTags: ["0.1.0.${RIO_BUILD_NUMBER}"]
          perApplication: false
          publish:
            - repo: docker.apple.com/gc-dmp-dev/mybusiness-frontend-v2

  - machine:
      baseImage: docker.apple.com/pie/fuji:latest
    branchName: main
    build:
      template: freestyle:v4:publish
      steps:
        - "true"
    package:
      dockerfile:
        - dockerfilePath: Dockerfile
          env:
            BASE_VERSION: "main_1.0.0.${RIO_BUILD_NUMBER}"
            BASE_ENV: "main"
          version: next
          extraTags: ["0.1.0.${RIO_BUILD_NUMBER}"]
          perApplication: false
          publish:
            - repo: docker.apple.com/gc-dmp-prod/mybusiness-frontend-v2
    finally:
      tag:
        expression: "1.0.0.${RIO_BUILD_NUMBER}"
