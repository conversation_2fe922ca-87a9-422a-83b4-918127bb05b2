<script setup>
import { RouterView, useRoute } from "vue-router";
import { computed, onMounted } from "vue";
import Sidebar from "./components/layout/Sidebar.vue";
import SidebarToggle from "@/components/layout/SidebarToggle.vue";
import { useAppStore } from "@/stores/app.js";
import FooterMenu from "./components/layout/FooterMenu.vue";
import AppFooter from "./components/AppFooter.vue";

const route = useRoute();
const appStore = useAppStore();

const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);

// 根据路由 meta 判断是否需要布局
const needsLayout = computed(() => {
  return route.meta?.requiresLayout !== false;
});

onMounted(async () => {
  appStore.initializeResponsiveState(); // 初始化响应式状态
  console.log("📱 响应式状态初始化完成");
});

// 关闭移动端菜单
function closeMobileMenu() {
  if (isMobile.value) {
    appStore.closeMobileMenu();
  }
}
</script>

<template>
  <div id="app" class="h-screen overflow-hidden relative">
    <!-- 需要布局的页面 -->
    <template v-if="needsLayout">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobile && shouldShowSidebar"
        class="fixed inset-0 bg-white/10 z-30 md:hidden"
        @click="closeMobileMenu"
      ></div>

      <!-- 左侧边栏 -->
      <Sidebar />

      <!-- 侧边栏切换按钮 -->
      <SidebarToggle />

      <!-- 主内容区域 -->
      <main
        :class="[
          'h-screen overflow-y-auto transition-all duration-300 overscroll-y-contain',
          isMobile ? 'ml-0' : shouldShowSidebar ? 'ml-[212px]' : 'ml-0',
        ]"
      >
        <Suspense>
          <!-- 主要内容容器 - 使用 flex 布局 -->
          <div class="min-h-full flex flex-col max-w-[1068px] mx-auto">
            <!-- 页面内容区域 - flex-grow 占用剩余空间 -->
            <div class="flex-grow py-4">
              <RouterView />
            </div>

            <!-- Footer 区域 - 固定在底部或跟随内容 -->
            <AppFooter />
          </div>
          <template #fallback>
            <div class="flex items-center justify-center h-64">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
          </template>
        </Suspense>
      </main>

      <!-- 移动端底部菜单 -->
      <FooterMenu v-if="isMobile" />
    </template>

    <!-- 不需要布局的页面（全屏显示） -->
    <template v-else>
      <Suspense>
        <RouterView />
        <template #fallback>
          <div class="flex items-center justify-center h-screen">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </template>
      </Suspense>
    </template>
  </div>
</template>

<style>
#app {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overscroll-behavior-y: contain;
  background: linear-gradient(180deg, #b9c2ca 0%, #e7ebed 100%);
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

main::-webkit-scrollbar {
  width: 6px;
}

main::-webkit-scrollbar-track {
  background: transparent;
}

main::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

main::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
