# API 配置和使用说明

## 📋 概述

本项目使用 axios 作为 HTTP 客户端，并配置了 Vite 代理来解决跨域问题。

## 🔧 配置说明

### Vite 代理配置

在 `vite.config.js` 中配置了以下代理，使用环境变量动态设置：

```javascript
proxy: {
  '/mybusiness': {
    target: env.VITE_API_TARGET || 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
  },
  '/auth': {
    target: env.VITE_API_TARGET || 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
  },
  '/upload': {
    target: env.VITE_API_TARGET || 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
  },
}
```

### 环境变量配置

项目支持通过环境变量灵活配置 API 相关设置：

| 环境变量                  | 默认值                  | 说明                     |
| ------------------------- | ----------------------- | ------------------------ |
| `VITE_API_BASE_URL`       | `/mybusiness`           | API 基础路径             |
| `VITE_API_TARGET`         | `http://localhost:8080` | 代理目标地址             |
| `VITE_API_TIMEOUT`        | `10000`                 | 请求超时时间（毫秒）     |
| `VITE_ENABLE_CONSOLE_LOG` | `true/false`            | 是否启用控制台日志       |
| `VITE_SHOW_DEBUG_INFO`    | `true/false`            | 是否显示详细调试信息     |
| `VITE_UPLOAD_MAX_SIZE`    | `10485760`              | 文件上传大小限制（字节） |

### 根据实际情况修改代理配置

1. **修改后端服务器地址**：

   ```javascript
   target: "http://your-api-server:port";
   ```

2. **如果后端不需要 `/api` 前缀**：

   ```javascript
   '/api': {
     target: 'http://localhost:8080',
     changeOrigin: true,
     secure: false,
     rewrite: (path) => path.replace(/^\/api/, ''),
   }
   ```

3. **添加多个服务代理**：
   ```javascript
   '/api/v1': {
     target: 'http://api-v1.example.com',
     changeOrigin: true,
   },
   '/api/v2': {
     target: 'http://api-v2.example.com',
     changeOrigin: true,
   }
   ```

## 🚀 使用方法

### 1. 基础用法

```javascript
import { get, post, put, del } from "@/utils/http.ts";

// GET 请求
const data = await get("/user/info");

// POST 请求
const result = await post("/user/create", { name: "张三", age: 25 });

// PUT 请求
const updated = await put("/user/123", { name: "李四" });

// DELETE 请求
await del("/user/123");
```

### 2. 使用 API 服务

```javascript
import { getDashboardData, getSalesData } from "@/api/index.js";

// 在组件中使用
const dashboardData = await getDashboardData();
const salesData = await getSalesData({ page: 1, size: 10 });
```

### 3. 完整的组件示例

```vue
<template>
  <div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else>
      <h2>销售数据</h2>
      <ul>
        <li v-for="item in salesData" :key="item.id">
          {{ item.name }}: {{ item.amount }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getSalesData } from "@/api/index.js";

const salesData = ref([]);
const loading = ref(false);

const fetchData = async () => {
  try {
    loading.value = true;
    const data = await getSalesData({
      startDate: "2024-01-01",
      endDate: "2024-12-31",
    });
    salesData.value = data;
  } catch (error) {
    console.error("获取数据失败：", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>
```

## 🔐 权限处理

axios 配置会自动在请求中添加权限信息：

### 请求头方式（推荐）

```javascript
// 自动添加到请求头
headers: {
  'RESELLER-ID': 'your-reseller-id',
  'RESELLER-RTM': 'your-reseller-rtm'
}
```

### 查询参数方式

如果后端需要查询参数，可以修改 `src/utils/http.ts` 中的请求拦截器：

```javascript
// 在请求拦截器中取消注释这部分代码
config.params = {
  ...config.params,
  RESELLER_ID: resellerId,
  RESELLER_RTM: resellerRtm,
};
```

## 🛠️ 错误处理

系统会自动处理以下错误：

- **401**: 权限不足，自动清除权限信息并跳转
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器内部错误
- **502/503/504**: 服务器不可用

## 📤 文件上传

```javascript
import { upload } from "@/utils/http.ts";

const handleFileUpload = async (file) => {
  const formData = new FormData();
  formData.append("file", file);

  try {
    const result = await upload("/upload", formData, (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total,
      );
      console.log(`上传进度: ${progress}%`);
    });
    console.log("上传成功:", result);
  } catch (error) {
    console.error("上传失败:", error);
  }
};
```

## 📁 文件下载

```javascript
import { download } from "@/utils/http.ts";

const handleDownload = async () => {
  try {
    await download("/export/sales", { type: "excel" }, "sales-data.xlsx");
  } catch (error) {
    console.error("下载失败:", error);
  }
};
```

## 🔄 自定义响应处理

如果后端返回的数据格式不同，可以修改 `src/utils/http.ts` 中的响应拦截器：

```javascript
// 示例：后端返回格式为 { success: boolean, data: any, msg: string }
if (data.success) {
  return data.data;
} else {
  ElMessage.error(data.msg);
  return Promise.reject(new Error(data.msg));
}
```

## 🌐 环境配置

可以根据不同环境配置不同的 API 地址：

```javascript
// 在 .env.development 中
VITE_API_BASE_URL=http://localhost:8080

// 在 .env.production 中
VITE_API_BASE_URL=https://api.production.com

// 在 http.ts 中使用
const http = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  // ... 其他配置
});
```

## 🔍 调试

开发环境下，所有的请求和响应都会在控制台输出，方便调试：

```
🚀 发送请求：/api/user/info { url: '/api/user/info', method: 'get' }
✅ 响应成功：/api/user/info { data: {...}, status: 200 }
```

生产环境下这些日志会被自动移除。
