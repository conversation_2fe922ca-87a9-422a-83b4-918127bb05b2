import { get } from "@/utils/http.ts";
import { ProductRankDialogResponse, ProductRankDialogRequest } from "./interface/ProductRankDialog";

// 获取用户信息
export const getUserInfo = () => {
  return get("/user/info");
};

// 获取reseller信息
export const getResellerInfo = () => {
  return get("/reseller/info");
};

// 获取 销售表现中间态
export const getSalesPerformanceDialogInfo = (params) => {
  return get("/hk_tw/datacenter/statistics", params);
};
// 获取 销售表现趋势图
export const getSalesEchartDialogInfo = (params) => {
  return get("/hk_tw/datacenter/sales/inter_trend", params);
};
export const getInventoryDialogInfo = (params) => {
  return get("/hk_tw/datacenter/inventory/details", params);
};

export const getInventoryEchartDialogInfo = (params) => {
  return get("/hktw/datacenter/inventory/trend", params);
};

// 产品热销排行榜 中间态
export const getProductRankDialogInfo = (params: ProductRankDialogRequest) => {
  return get<ProductRankDialogResponse>("/hk_tw/product_rank/inter_rank", params);
};

// 健康诊断 中间态
export const getScoreDetailsDialogInfo = (params) => {
  return get("/hk_tw/health/score_details", params);
};
// 门店评分排行榜 中间态
export const getStoreRankDialogInfo = (params) => {
  return get("/hk_tw/store_rank/show", params);
};
//销售预警 预警分布
export const getSalesAlertDialogInfo = (params) => {
  return get("/hk_tw/health/so_warning/inventory_distribution");
};

//销售预警 健康度趋势
export const getSalesAlertTrendInfo = (params) => {
  return get("/hk_tw/health/so_warning/health_trend");
};

//库存提醒
export const getInventoryRemainderInfo = (params) => {
  return get("/hk_tw/health/inv_remind/inventory_distribution");
};

//库存预警
export const getInventoryWarningInfo = (params) => {
  return get("/hk_tw/health/inv_warning/inventory_distribution");
};
