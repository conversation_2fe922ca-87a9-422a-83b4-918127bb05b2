<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Group</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.959972568" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.884397493" offset="12.3838292%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.08" offset="19.6571611%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.08" offset="80.1026022%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.4" offset="87.587648%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.4" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="40" height="40" rx="20"></rect>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-3.8%" y="-3.8%" width="107.5%" height="107.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.32 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="MyBusiness_HK/TW-v1.1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Components" transform="translate(-268, -2013)">
            <g id="Group" transform="translate(268, 2013)">
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill-opacity="0.32" fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                    <rect stroke="url(#linearGradient-1)" stroke-width="0.5" stroke-linejoin="square" x="0.25" y="0.25" width="39.5" height="39.5" rx="19.75"></rect>
                </g>
                <g id="􀆄" transform="translate(10, 10)" fill="#1C1C1E" fill-rule="nonzero">
                    <path d="M4.4531855,15.3839493 C4.56167549,15.4883957 4.68683571,15.5584145 4.82866615,15.5940056 C4.97049659,15.6295967 5.11167854,15.6295967 5.25221199,15.5940056 C5.39274544,15.5584145 5.51390022,15.4897308 5.61567634,15.3879547 L10.1178965,10.8857346 L14.6173701,15.3865814 C14.7209772,15.4901886 14.8430285,15.5595589 14.9835238,15.5946922 C15.1240191,15.6298256 15.2655253,15.6301498 15.4080424,15.595665 C15.5505595,15.5611801 15.6740412,15.4901505 15.7784876,15.382576 C15.8830103,15.2780533 15.952495,15.1553154 15.9869418,15.0143623 C16.0213885,14.8734093 16.0218272,14.7326851 15.9882578,14.5921898 C15.9546885,14.4516945 15.886558,14.3296432 15.7838663,14.2260361 L11.2843928,9.71923828 L15.7838663,5.21976471 C15.886558,5.11615753 15.9556994,4.99410629 15.9912905,4.85361099 C16.0268816,4.71311569 16.0264429,4.57260132 15.9899744,4.43206787 C15.9535059,4.29153442 15.8830103,4.16904449 15.7784876,4.06459808 C15.6699976,3.95610809 15.545505,3.88462067 15.4050097,3.8501358 C15.2645144,3.81565094 15.1240191,3.81519318 14.9835238,3.84876251 C14.8430285,3.88233185 14.7209772,3.95248413 14.6173701,4.05921936 L10.1178965,8.55869293 L5.61567634,4.05784607 C5.51390022,3.95294189 5.39175361,3.88370514 5.24923652,3.8501358 C5.10671943,3.81656647 4.96454567,3.81656647 4.82271522,3.8501358 C4.68088478,3.88370514 4.5577082,3.9547348 4.4531855,4.06322479 C4.34957832,4.1677475 4.28099006,4.29069519 4.24742073,4.43206787 C4.21385139,4.57344055 4.21385139,4.71460342 4.24742073,4.85555649 C4.28099006,4.99650955 4.34824318,5.11745453 4.44918007,5.21839142 L8.95128578,9.71923828 L4.44918007,14.2287827 C4.34824318,14.3296432 4.27976936,14.4503403 4.24375862,14.5908737 C4.20774788,14.7314072 4.20675606,14.8725891 4.24078315,15.0144196 C4.27481025,15.15625 4.34561104,15.2794266 4.4531855,15.3839493 Z" id="路径"></path>
                </g>
            </g>
        </g>
    </g>
</svg>