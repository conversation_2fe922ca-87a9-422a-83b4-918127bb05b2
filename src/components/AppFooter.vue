<script setup lang="ts">
interface Props {
  text?: string;
  showLinks?: boolean;
}

const { text = "每日數據預計將在12:00更新完畢，可能出現數據更新延遲。", showLinks = false } =
  defineProps<Props>();

function handleLinkClick(type: "manual" | "terms") {
  console.log(`点击了${type === "manual" ? "使用手册" : "使用条款"}`);
}
</script>

<template>
  <footer class="flex-shrink-0 text-[12px] text-[#3A3A3C] font-normal py-[12px] mt-auto">
    <div v-if="showLinks" class="flex justify-between items-center">
      <div>{{ text }}</div>
      <div class="flex items-center gap-[16px] text-[#0077ED]">
        <span class="cursor-pointer hover:underline" @click="handleLinkClick('manual')">
          使用手册
        </span>
        <span class="cursor-pointer hover:underline" @click="handleLinkClick('terms')">
          使用条款
        </span>
      </div>
    </div>
    <div v-else>{{ text }}</div>
  </footer>
</template>
