<script setup lang="ts">
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user.js";
import { menuItems as routeMenuItems } from "@/config/menu.js";
import Dashboard from "@/assets/icons/menu/mobile/dashbraod.svg";
import Message from "@/assets/icons/menu/mobile/message.svg";
import Data from "@/assets/icons/menu/mobile/data.svg";
import Forecast from "@/assets/icons/menu/mobile/forecast.svg";
import Operations from "@/assets/icons/menu/mobile/operations.svg";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const menuIcons = {
  dashboard: Dashboard,
  message: Message,
  data: Data,
  forecast: Forecast,
  operations: Operations,
};

// 菜单项配置 - 从路由文件导入并根据权限过滤
const filteredMenus = userStore.userRtm ? userStore.filterMenuItems(routeMenuItems) : [];

console.log("📱 移动端菜单项", filteredMenus);
const disabledTabs = ref([]);
const findTopRoute = () => {
  return routeMenuItems.find((item) => {
    return item.path === route.path || item.children?.find((child) => child.path === route.path);
  });
};
const defaultTab = ref(findTopRoute()?.path);

const menuTabs = computed(() => {
  return routeMenuItems.map((item) => ({
    id: item.id,
    label: item.title,
    value: item.path,
    icon: item.mobileIcon,
  }));
});

const handleTabChange = (val: string) => {
  router.push(val);
  defaultTab.value = val;
};
</script>

<template>
  <div class="fixed w-full left-0 bottom-0 z-50 bg-transparent">
    <div class="glass-card h-[56px] mx-[16px] !p-0 !rounded-[28px]">
      <dmp-tab-switcher
        :tabs="menuTabs"
        :equal-width="true"
        :tab-height="56"
        :default-tab="defaultTab"
        bg-color="transparent"
        mode="big-large"
        slide-class="!bg-[#0071E3]/10"
      >
        <template #btns="{ tab, activeTab }">
          <div
            :class="[
              'w-full rounded-[24px] flex flex-col gay-y-[2px] items-center justify-center cursor-pointer text-[12px] p-[4px]',
              activeTab === tab.value ? 'active text-[#0071E3]' : 'text-[#6E6E73]',
              disabledTabs.includes(tab.value) ? 'disabled-tab text-[#6E6E73]/10' : '',
            ]"
            @click="() => handleTabChange(tab.value)"
          >
            <component :is="menuIcons[tab.id]" class="w-[48px] h-[24px]" />
            <p class="leading-14">
              {{ tab.label }}
            </p>
          </div>
        </template>
      </dmp-tab-switcher>
    </div>
  </div>
</template>

<style scoped>
.disabled-tab {
  cursor: not-allowed;
  opacity: 0.6;
}
.glass-card {
  border-radius: 28px !important;
}
.glass-card::before {
  border-radius: 28px !important;
}
.glass-card::after {
  border-radius: 28px !important;
}
</style>
