<script setup>
import { ref, computed } from "vue";
import { RouterLink, useRoute } from "vue-router";
import { useUserStore, useAppStore } from "@/stores";

const route = useRoute();
const userStore = useUserStore();
const appStore = useAppStore();

const navigationItems = [
  { name: "首页", path: "/", icon: "🏠" },
  { name: "仪表盘", path: "/dashboard", icon: "📊" },
  { name: "关于", path: "/about", icon: "ℹ️" },
];

const isCurrentRoute = (path) => {
  return route.path === path;
};

function handleLogin() {
  userStore.login({ username: "demo" });
}

function handleLogout() {
  userStore.logout();
}

// 当前日期
const currentDate = computed(() => {
  const now = new Date();
  return `${now.getMonth() + 1}月${now.getDate()}日`;
});

// 公司信息
const companyInfo = ref({
  name: "北京英龙华辰科技有限公司",
  status: "11",
  points: "425",
  totalPoints: "22,750",
  percentage: "15%",
});

// 用户信息
const userInfo = ref({
  name: "管理员",
  avatar: null,
});

// 搜索关键词
const searchKeyword = ref("");

// 通知数量
const notificationCount = computed(() => {
  return appStore.notifications?.length || 0;
});

// 搜索功能
function handleSearch() {
  if (searchKeyword.value.trim()) {
    console.log("搜索:", searchKeyword.value);
    // 这里可以实现搜索逻辑
  }
}

// 用户菜单
function showUserMenu() {
  console.log("显示用户菜单");
}

// 通知中心
function showNotifications() {
  console.log("显示通知");
}
</script>

<template>
  <header class="h-16 flex items-center justify-between px-6 relative z-10">
    <!-- 左侧：日期和公司信息 -->
    <div class="flex items-center space-x-6">
      <!-- 日期 -->
      <div class="flex items-center space-x-2 text-gray-600">
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        <span class="font-medium">{{ currentDate }}</span>
      </div>

      <!-- 公司信息 -->
      <div class="flex items-center space-x-4">
        <h1 class="text-xl font-semibold text-gray-900">
          {{ companyInfo.name }}
        </h1>
        <div class="flex items-center space-x-2">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            {{ companyInfo.status }}
          </span>
        </div>
      </div>
    </div>

    <!-- 右侧：积分信息、搜索、通知、用户 -->
    <div class="flex items-center space-x-6">
      <!-- 积分信息 -->
      <div class="flex items-center space-x-4 bg-gray-50 px-4 py-2 rounded-lg">
        <div class="flex items-center space-x-2">
          <div
            class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-4 h-4 text-blue-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
              />
            </svg>
          </div>
          <div class="text-sm">
            <div class="font-semibold text-gray-900">
              本周积分 {{ companyInfo.points }}
            </div>
            <div class="text-gray-500">
              全部积分 {{ companyInfo.percentage }} 总积分
              {{ companyInfo.totalPoints }}
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="relative">
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <svg
            class="h-5 w-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <input
          v-model="searchKeyword"
          @keyup.enter="handleSearch"
          type="text"
          placeholder="搜索..."
          class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>

      <!-- 通知 -->
      <button
        @click="showNotifications"
        class="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 17h5l-5 5v-5zM15 17H9a4 4 0 01-4-4V7a4 4 0 014-4h6a4 4 0 014 4v6a4 4 0 01-4 4z"
          />
        </svg>
        <span
          v-if="notificationCount > 0"
          class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full"
        >
          {{ notificationCount }}
        </span>
      </button>

      <!-- 用户信息 -->
      <div class="relative">
        <button
          @click="showUserMenu"
          class="flex items-center space-x-[3px] p-[2px] rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div
            class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-700">{{
            userInfo.name
          }}</span>
          <svg
            class="w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      </div>
    </div>
  </header>
</template>

<style scoped>
/* 确保搜索框在小屏幕上的响应式行为 */
@media (max-width: 768px) {
  .relative input {
    width: 200px;
  }
}

@media (max-width: 640px) {
  .relative input {
    width: 150px;
  }
}
</style>
