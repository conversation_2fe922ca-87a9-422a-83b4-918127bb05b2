<script setup>
import { useAppStore } from "@/stores/app.js";

const appStore = useAppStore();

const isMobile = computed(() => appStore.isMobile);
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <ItemWithArrow>
    <div class="flex flex-col gap-[2px]">
      <div
        :class="[
          'flex flex-col gap-[16px] text-[rgba(28,28,30,.64)]',
          isMobile ? 'text-[11px]' : 'text-[12px]',
        ]"
      >
        {{ item.title }}
      </div>

      <div class="flex gap-[4px] text-[#1C1C1E] items-end">
        <span
          :class="[
            'text-[#1C1C1E] leading-[20px] font-semibold',
            isMobile ? 'text-[16px]' : 'text-[18px]',
          ]"
          >12</span
        >
        <span
          :class="[
            'leading-[16px] font-medium',
            isMobile ? 'text-[9px]' : 'text-[10px]',
          ]"
          >条未读</span
        >
      </div>
    </div>
  </ItemWithArrow>
</template>

<style scoped></style>
