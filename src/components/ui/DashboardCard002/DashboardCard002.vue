<script setup>
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="item.title">
    <div class="flex flex-col gap-[8px]">
      <div class="flex flex-col gap-[2px]">
        <div class="text-[12px] text-[rgba(28,28,30,.64)]">Q3W9异常门店数</div>
        <div class="flex items-end gap-[1px]">
          <div class="text-[18px] text-[#1C1C1E] font-medium leading-[20px]">
            0
          </div>
          <div class="text-[10px] text-[#1C1C1E] font-medium leading-[16px]">
            家
          </div>
        </div>
      </div>

      <div class="border-b border-[#E5E5E5]"></div>

      <div>
        <div class="text-[12px] text-[rgba(28,28,30,.64)]">Q3W9异常门店数</div>
        <div class="flex items-end gap-[1px]">
          <div class="text-[18px] text-[#1C1C1E] font-bold leading-[20px]">
            0
          </div>
          <div class="text-[10px] text-[#1C1C1E] font-medium leading-[16px]">
            家
          </div>
        </div>
      </div>
    </div>
  </CardWithTitle>
</template>
