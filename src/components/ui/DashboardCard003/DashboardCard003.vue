<script setup>
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="item.title">
    <div class="flex flex-col gap-[8px]">
      <div class="flex flex-col gap-[2px]">
        <div class="text-[12px] text-[rgba(28,28,30,.64)]">申报中</div>
        <div class="flex items-end gap-[1px] text-[#1890CC]">
          <div class="text-[24px] font-medium leading-[20px]">0</div>
          <div class="text-[10px] font-medium leading-[10px]">家</div>
        </div>
      </div>
    </div>
  </CardWithTitle>
</template>
