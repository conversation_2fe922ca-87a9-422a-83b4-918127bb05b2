<script setup>
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="item.title">
    <div class="flex flex-col gap-[8px]">
      <div class="flex flex-col gap-[2px]">
        <div>
          <div class="text-[12px] text-[rgba(28,28,30,.64)]">Q2W9 - Q3W10</div>
          <div class="text-[12px] text-[rgba(28,28,30,.64)]">项目达成率</div>
        </div>
        <div class="flex items-end gap-[1px]">
          <div class="text-[18px] text-[#1C1C1E] font-medium leading-[20px]">
            98
          </div>
          <div class="text-[10px] text-[#1C1C1E] font-medium leading-[16px]">
            %
          </div>
        </div>
      </div>
    </div>
  </CardWithTitle>
</template>
