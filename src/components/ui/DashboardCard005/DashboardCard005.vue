<script setup>
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="item.title">
    <div class="flex flex-col gap-[8px]">
      <div class="flex flex-col gap-[2px]">
        <div class="text-[12px] text-[rgba(28,28,30,.64)]">申报中</div>
        <div class="flex items-end gap-[1px] text-[#1890CC]">
          <div class="text-[18px] font-bold leading-[20px]">0</div>
          <div class="text-[10px] font-medium leading-[16px]">家</div>
        </div>
      </div>

      <div class="flex flex-col gap-[2px]">
        <div class="text-[12px] text-[rgba(28,28,30,.64)]">待启动</div>
        <div class="flex items-end gap-[1px] text-[#FF9500]">
          <div class="text-[18px] font-medium leading-[20px]">0</div>
          <div class="text-[10px] font-medium leading-[16px]">家</div>
        </div>
      </div>
    </div>
  </CardWithTitle>
</template>
