<script setup>
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
});

// 数据分组 4个一组
const groupedData = computed(() => {
  if (!props.data || props.data.length === 0) return [];

  const groups = [];
  for (let i = 0; i < props.data.length; i += 4) {
    groups.push(props.data.slice(i, i + 4));
  }
  return groups;
});
</script>

<template>
  <CardWithTitle :title="title">
    <el-carousel
      class="w-full"
      height="80px"
      :indicator-position="groupedData.length > 1 ? 'outside' : 'none'"
      :autoplay="false"
    >
      <el-carousel-item v-for="(group, index) in groupedData" :key="index">
        <div class="flex items-center h-full">
          <div
            v-for="(item, itemIndex) in group"
            :key="itemIndex"
            class="flex-1 min-w-0 flex items-center justify-center"
          >
            <div
              :class="[
                'flex flex-col gap-[8px] items-center justify-center w-full',
                itemIndex !== group.length - 1 ? 'has-border-right' : '',
              ]"
            >
              <div class="w-[20px] h-[20px] bg-[#1c1c1e] rounded-full"></div>

              <div class="flex flex-col items-center justify-center gap-[4px]">
                <div
                  class="text-[18px] flex items-end text-[#1c1c1e] font-medium"
                >
                  <div
                    class="text-[18px] leading-[16px] text-[#1c1c1e] font-medium"
                  >
                    {{ item.number }}
                  </div>
                  <div
                    class="text-[10px] text-[#1c1c1e] font-bold leading-[12px]"
                  >
                    台
                  </div>
                </div>

                <div class="text-[12px] text-[rgba(28,28,30,.64)]">
                  {{ item.lob }}
                </div>
              </div>
            </div>
          </div>
          <!-- 空div占位 防止布局错乱-->
          <div
            v-for="n in 4 - group.length"
            :key="`empty-${n}`"
            class="flex-1 min-w-0"
          ></div>
        </div>
      </el-carousel-item>
    </el-carousel>
    <slot name="footer" />
  </CardWithTitle>
</template>

<style scoped>
:deep(.el-carousel__indicators) {
  width: fit-content;
  background: rgba(28, 28, 30, 0.07);
  border-radius: 8px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 0 11px;
}
:deep(.el-carousel__button) {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(28, 28, 30, 0.24);
}
:deep(.el-carousel__indicator) {
  padding: 0 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}
:deep(.is-active .el-carousel__button) {
  width: 16px;
  background: rgba(28, 28, 30, 0.64);
  border-radius: 8px;
}
.has-border-right {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    right: -1px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 48px;
    background: #dee0e2;
  }
}
</style>
