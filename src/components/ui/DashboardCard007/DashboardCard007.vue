<script setup>
defineProps({
  item: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="item.title">
    <div class="flex flex-col gap-[4px]">
      <div class="flex items-end text-[#1C1C1E]">
        <div class="text-[22px] font-bold leading-[24px]">2700</div>
        <div class="text-[10px] font-medium leading-[16px]">人</div>
      </div>

      <div class="flex items-center gap-[2px]">
        <div class="text-[11px] text-[rgba(28,28,30,.64)]">较上周同期</div>
        <div class="text-[11px] text-[#F63F54]">+10%</div>
      </div>
    </div>
  </CardWithTitle>
</template>
