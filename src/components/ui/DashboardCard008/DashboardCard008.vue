<!--
 * @Date: 2025-07-04 10:45:51
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-04 15:08:55
 * @FilePath: /MyBusinessV2/src/components/ui/DashboardCard008/DashboardCard008.vue
-->
<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
defineProps({
  data: {
    type: Array,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
});
</script>

<template>
  <CardWithTitle :title="title">
    <div class="flex flex-col gap-[8px]">
      <div class="flex">
        <div
          v-for="(item, index) in data"
          :key="item.lob"
          :class="
            index === data.length - 1
              ? 'flex-1 py-[4px] px-[14px]'
              : 'flex-1 py-[4px] px-[14px] item-wrapper'
          "
        >
          <div class="flex items-center text-[#1C1C1E] flex-col gap-[12px]">
            <div class="flex items-center justify-center gap-[4px]">
              <div class="w-[16px] h-[16px] bg-[black] rounded-full"></div>
              <div
                class="text-[12px] font-medium text-[#1C1C1E] leading-[16px]"
              >
                {{ item.lob }}
              </div>
            </div>

            <div class="flex items-center justify-between w-full">
              <div
                class="text-[11px] w-[70px] text-[rgba(28,28,30,.64)] flex flex-col justify-center items-center"
              >
                <ValueWithUnit :value="item.currentWeek" unit="万台" />
                <div
                  class="text-[10px] text-[rgba(28,28,30,.64)] font-medium leading-[16px]"
                >
                  {{ t("本周") }}
                </div>
              </div>
              <div
                class="text-[11px] w-[70px] text-[rgba(28,28,30,.64)] flex flex-col justify-center items-center"
              >
                <ValueWithUnit :value="item.quarter" unit="台" />
                <div
                  class="text-[10px] text-[rgba(28,28,30,.64)] font-medium leading-[16px]"
                >
                  {{ t("季度累计") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <slot name="footer" />
    </div>
  </CardWithTitle>
</template>

<style scoped>
.item-wrapper {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 1px;
    height: 56px;
    background: rgba(28, 28, 28, 0.12);
  }
}
</style>
