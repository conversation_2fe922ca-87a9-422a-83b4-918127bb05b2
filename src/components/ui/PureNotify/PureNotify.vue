<template>
  <div>
    <Teleport to="body">
      <div
        v-if="showMask"
        class="pure-notify-mask"
        @click="closeOnClickModal ? close() : null"
      ></div>
    </Teleport>
    <div
      :class="['notification notification-shadow ', itemWrapperClass, type]"
      @mouseenter="pauseTimer"
      @mouseleave="resumeTimer"
      v-if="!content"
    >
      <div
        :class="[
          { 'p-[16px] gap-[16px]': !pureMode },
          ' flex items-center justify-between w-full',
        ]"
      >
        <div class="flex items-center gap-[12px]">
          <div class="shrink-0">
            <img
              class="icon"
              :class="iconClass"
              :src="icon"
              alt=""
              :style="{ height: iconHeight }"
            />
          </div>
          <div class="content" ref="textRef">
            {{ message }}
          </div>
        </div>
        <div
          class="timer-container shrink-0"
          :style="{ height: iconHeight }"
          v-if="showTimer"
        >
          <Transition name="time-fade">
            <label class="timer" :key="remainingTime"
              >{{ remainingTime }}s</label
            >
          </Transition>
        </div>
      </div>
    </div>
    <div v-else>
      <div
        v-if="typeof content === 'object' && toastLayout === 'horizontal'"
        :class="itemWrapperClass"
      >
        <div class="shrink-0">
          <img
            class="icon"
            :class="iconClass"
            :src="icon"
            alt=""
            :style="{ height: iconHeight }"
          />
        </div>
        <component :is="content" ref="textRef" />
        <div>
          <div
            class="timer-container shrink-0"
            v-if="showTimer"
            :style="{ height: iconHeight }"
          >
            <Transition name="time-fade">
              <label class="timer" :key="remainingTime"
                >{{ remainingTime }}s</label
              >
            </Transition>
          </div>
          <div
            v-if="confirmButton?.show || cancelButton?.show"
            class="flex items-center gap-[12px]"
          >
            <div v-if="!buttons.length" class="flex items-center gap-[12px]">
              <button
                v-if="cancelButton?.show"
                @click="onCancel"
                :class="
                  cancelButton?.class
                    ? cancelButton?.class
                    : 'text-[12px] text-white cancel-button bg-[rgba(255,255,255,.24)] flex justify-center items-center '
                "
                :style="{
                  height: iconHeight,
                  minWidth: iconHeight,
                  aspectRatio: cancelButton?.ratio,
                }"
              >
                <img
                  v-if="!cancelButton?.text"
                  src="./assets/cancel.png"
                  class="w-[30%] block"
                  alt=""
                />
                <label v-else>{{ cancelButton?.text }}</label>
              </button>
              <button
                v-if="confirmButton?.show"
                @click="onConfirm"
                :class="
                  confirmButton?.class
                    ? confirmButton?.class
                    : 'text-[14px] text-white confirm-button bg-[rgba(255,255,255)] flex justify-center items-center '
                "
                :style="{
                  height: iconHeight,
                  minWidth: iconHeight,
                  aspectRatio: confirmButton?.ratio,
                }"
              >
                <img
                  v-if="!cancelButton?.text"
                  src="./assets/confirm.png"
                  class="w-[40%] block"
                  alt=""
                />
                <label v-else>{{ confirmButton?.text }}</label>
              </button>
            </div>
            <div v-else>
              <button
                v-for="button in buttons"
                :key="button.text"
                @click="button.onClick"
                :class="button.class"
              >
                {{ button.text }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="typeof content === 'object' && toastLayout === 'vertical'"
        :class="[itemWrapperClass, 'flex flex-col']"
      >
        <component :is="content" ref="textRef" />
        <div class="flex items-center justify-between w-full">
          <div class="shrink-0">
            <img
              class="icon"
              :class="iconClass"
              :src="icon"
              alt=""
              :style="{ height: iconHeight }"
            />
          </div>
          <div>
            <div
              class="timer-container shrink-0"
              v-if="showTimer"
              :style="{ height: iconHeight }"
            >
              <Transition name="time-fade">
                <label class="timer" :key="remainingTime"
                  >{{ remainingTime }}s</label
                >
              </Transition>
            </div>
            <div
              v-if="confirmButton?.show || cancelButton?.show"
              class="flex items-center gap-[14px]"
            >
              <div v-if="!buttons.length" class="flex items-center gap-[14px]">
                <button
                  v-if="cancelButton?.show"
                  @click="onCancel"
                  :class="
                    cancelButton?.class
                      ? cancelButton?.class
                      : 'text-[14px] text-white cancel-button bg-[rgba(255,255,255,.24)]'
                  "
                  :style="{
                    height: iconHeight,
                    minWidth: iconHeight,
                    aspectRatio: cancelButton?.ratio,
                  }"
                >
                  <img
                    v-if="!cancelButton?.text"
                    src="./assets/cancel.png"
                    class="w-[30%] block"
                    alt=""
                  />
                  <label v-else>{{ cancelButton?.text }}</label>
                </button>
                <button
                  v-if="confirmButton?.show"
                  @click="onConfirm"
                  :class="
                    confirmButton?.class
                      ? confirmButton?.class
                      : 'text-[14px] text-white confirm-button bg-[rgba(255,255,255)]'
                  "
                  :style="{
                    height: iconHeight,
                    minWidth: iconHeight,
                    aspectRatio: confirmButton?.ratio,
                  }"
                >
                  <img
                    v-if="!confirmButton?.text"
                    src="./assets/confirm.png"
                    class="w-[40%] block"
                    alt=""
                  />
                  <label v-else>{{ confirmButton?.text }}</label>
                </button>
              </div>
              <div v-else>
                <button
                  v-for="button in buttons"
                  :key="button.text"
                  @click="button.onClick"
                  :class="button.class"
                >
                  <img
                    v-if="!button.text"
                    src="./assets/confirm.png"
                    class="w-[40%] block"
                    alt=""
                  />
                  <label v-else>{{ button.text }}</label>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import successIcon from "./assets/success.png";
import errorIcon from "./assets/error.png";
import infoIcon from "./assets/default.png";
import warningIcon from "./assets/warning.png";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import cancelIcon from "./assets/cancel.png";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import confirmIcon from "./assets/confirm.png";

const props = defineProps({
  message: { type: String, default: "" },
  content: { type: [String, Object, Function], default: null },
  type: { type: String, default: "info" },
  id: { type: String, default: "" },
  duration: { type: Number, default: 3000 },
  onClose: { type: Function, required: true },
  alwaysShow: { type: Boolean, default: false },
  pureMode: { type: Boolean, default: false },
  toastLayout: { type: String, default: "horizontal" },
  itemWrapperClass: { type: String, default: "" },
  iconClass: { type: String, default: "" },
  buttons: { type: Array, default: () => [] },
  onCancel: { type: Function, default: () => {} },
  onConfirm: { type: Function, default: () => {} },
  onDestroyed: { type: Function, default: () => {} },
  showMask: { type: Boolean, default: false },
  customIcon: { type: String, default: null },
  confirmButton: {
    type: Object,
    default: () => {
      return {
        show: false,
        ratio: "1/1",
      };
    },
  },
  cancelButton: {
    type: Object,
    default: () => {
      return {
        show: false,
        ratio: "1/1",
      };
    },
  }, // 取消按钮
  closeOnClickModal: { type: Boolean, default: false }, // 点击蒙层是否关闭
  width: {
    type: [String, Number],
    validator: (value) => {
      // 如果值是数字，检查是否为正数
      if (typeof value === "number") {
        if (value <= 0) {
          console.warn("[PureNotify] Width number must be positive");
          return false;
        }
        return true;
      }

      // 如果字符串可以转换为有效的正数
      if (!isNaN(value) && Number(value) > 0) {
        return true;
      }

      // 如果字符串包含单位，检查格式并验证数字
      const pattern = /^[\d.]+(?:px|rem|em|vw|%)$/i;
      if (!pattern.test(value)) {
        console.warn(
          "[PureNotify] Width string must end with px, rem, em, vw, or %",
        );
        return false;
      }

      const number = parseFloat(value);
      if (
        isNaN(number) ||
        number <= 0 ||
        !/^\d*\.?\d+$/.test(number.toString())
      ) {
        console.warn("[PureNotify] Invalid number in width string");
        return false;
      }

      return true;
    },
  }, // 消息宽度
});

const emit = defineEmits(["close"]);
const remainingTime = ref(props.duration / 1000);
let rafId = null;
let startTime = null;
let pausedTimeRemaining = null;

const close = () => {
  emit("close");
  props.onClose();
};

const icon = computed(() => {
  const componentsEnum = {
    success: successIcon,
    error: errorIcon,
    info: infoIcon,
    warning: warningIcon,
  };
  return props.customIcon || componentsEnum[props.type] || componentsEnum.info;
});

const updateTimer = (timestamp) => {
  if (!startTime) startTime = timestamp;
  const elapsed = timestamp - startTime;
  remainingTime.value = Math.ceil(pausedTimeRemaining - elapsed / 1000);

  if (remainingTime.value <= 0) {
    close();
    return;
  }

  rafId = requestAnimationFrame(updateTimer);
};

const showTimer = computed(
  () =>
    props.duration > 0 && !props.confirmButton.show && !props.cancelButton.show,
);

const textRef = ref(null);

const iconHeight = ref(`24px`);

const startTimer = () => {
  if (!showTimer.value) return;

  startTime = null;
  pausedTimeRemaining = props.duration / 1000;
  rafId = requestAnimationFrame(updateTimer);
};

const pauseTimer = () => {
  if (!showTimer.value || !rafId) return;

  cancelAnimationFrame(rafId);
  pausedTimeRemaining = remainingTime.value;
};

const resumeTimer = () => {
  if (!showTimer.value) return;

  startTime = null;
  rafId = requestAnimationFrame(updateTimer);
};

const hideScroll = () => {
  document.body.style.overflow = "hidden";
};

const showScroll = () => {
  document.body.style.overflow = "auto";
};

const resizeObserver = new ResizeObserver(() => {
  initIconHeight();
});

const initIconHeight = () => {
  let textHeight = textRef.value?.offsetHeight || 24;
  if (textHeight > 40) {
    textHeight = 40;
  }
  iconHeight.value = `${textHeight}px`;
};

onMounted(() => {
  startTimer();
  nextTick(() => {
    initIconHeight();
    if (textRef.value) {
      resizeObserver.observe(textRef.value);
    }

    if (props.showMask) {
      hideScroll();
    }
  });
});

onUnmounted(() => {
  if (rafId) {
    cancelAnimationFrame(rafId);
  }
  if (textRef.value) {
    resizeObserver.unobserve(textRef.value);
  }

  if (props.showMask) {
    showScroll();
  }
  if (props.onDestroyed) {
    props.onDestroyed();
  }
});
</script>

<style lang="less">
.notification {
  position: relative;
  width: 100%;
  min-height: 56px;
  background-color: #1c1c1e;
  color: #ffffff;
  margin-top: 16px;
  box-sizing: border-box;
  border-radius: 56px;
  display: flex;

  align-items: center;
  font-size: 14px;
  line-height: 22px;
  box-shadow:
    0 10px 24px 0 rgba(0, 0, 0, 0.15),
    // 主阴影，更柔和
    0 2px 4px 0 rgba(0, 0, 0, 0.05); // 近距离小阴影，增加层次感
  transform: translateZ(0); // 启用硬件加速
  transition: all 0.3s cubic-bezier(0.32, 0.72, 0, 1);
  will-change: transform, opacity, box-shadow;

  &:hover {
    transform: translateY(-3px) scale(1.02) translateZ(0);
    box-shadow:
      0 14px 28px 0 rgba(0, 0, 0, 0.2),
      0 4px 8px 0 rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(-1px) scale(0.98) translateZ(0);
    transition-duration: 0.1s;
  }

  &.success {
    background-color: #1c1c1e;
    color: #ffffff;
  }

  &.error {
    background-color: #1c1c1e;
    color: #ffffff;
  }
}

.notification-shadow {
  box-shadow: 0 10px 24px 0 rgba(0, 0, 0, 0.4);
}

.pure-notify {
  position: fixed;
  padding: 0;
  width: 300px;
  left: 50%;
  transform: translateX(-50%);
  list-style-type: none;
  top: 0px;
  z-index: 9999;
  pointer-events: none;
  transform-origin: top;

  > * {
    pointer-events: all !important;
  }
  > div {
    width: 300px;
  }
}
/* 1. 声明过渡效果 */
.dynamic-fade-move,
.dynamic-fade-enter-active,
.dynamic-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dynamic-fade-enter-from {
  opacity: 0;
  transform: translateY(-50px) scale(0.9);
}

.dynamic-fade-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.dynamic-fade-leave-to {
  opacity: 0;
  transform: translateY(-25px) scale(1);
}

.dynamic-fade-leave-active {
  position: absolute;
  width: 100%;
  left: 0;
  pointer-events: none;
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}
.pure-notify--vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
}
/* prettier-ignore */
.timer-container {
  min-height: 24px;
  border-radius: 24px;
  min-width: 40px;
  background-color: rgba(255, 255, 255, 0.24);
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1/1;
}
.timer {
  color: #fff;
  display: inline-block;
  font-size: 13px;
  line-height: 20px;
}
.time-fade-enter-active,
.time-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.32, 0.72, 0, 1);
  position: absolute;
}

.time-fade-enter-from {
  opacity: 0;
  transform: translateY(12px) scale(0.7);
}

.time-fade-leave-to {
  opacity: 0;
  transform: translateY(-12px) scale(0.7);
}

.time-fade-enter-to,
.time-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
.icon {
  aspect-ratio: 1/1;
  flex-shrink: 0;
}
.cancel-button {
  aspect-ratio: 1/1;
  flex-shrink: 0;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm-button {
  aspect-ratio: 1/1;
  flex-shrink: 0;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pure-notify-fade-enter-active,
.pure-notify-fade-leave-active {
  transition: opacity 0.3s ease;
}

.pure-notify-fade-enter-from,
.pure-notify-fade-leave-to {
  opacity: 0;
}

.pure-notify-fade-enter-to {
  opacity: 1;
}

.pure-notify-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.58);
  z-index: 9998;
}
</style>

<style>
.pure-notify-mask-fade-enter-active,
.pure-notify-mask-fade-leave-active {
  transition: opacity 0.3s ease;
}

.pure-notify-mask-fade-enter-from,
.pure-notify-mask-fade-leave-to {
  opacity: 0;
}

.pure-notify-mask-fade-enter-to,
.pure-notify-mask-fade-leave-from {
  opacity: 1;
}
</style>
