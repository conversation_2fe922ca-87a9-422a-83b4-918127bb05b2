<template>
  <div class="checkmark-container">
    <svg viewBox="0 0 24 24" fill="none">
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="#1C9B4A"
        stroke-width="2"
        class="circle"
      />
      <path
        d="M8 12.5L11 15.5L16 9.5"
        stroke="#1C9B4A"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="checkmark"
      />
    </svg>
  </div>
</template>

<script setup></script>

<style scoped lang="less">
/* prettier-ignore */
.checkmark-container {
  width: 24PX;
  height: 24PX;
  position: relative;
  
  .circle {
    stroke-dasharray: 76;
    stroke-dashoffset: 76;
    animation: circle-animation 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .checkmark {
    stroke-dasharray: 18;
    stroke-dashoffset: 18;
    animation: checkmark-animation 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards 0.4s;
  }
}

@keyframes circle-animation {
  from {
    stroke-dashoffset: 76;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes checkmark-animation {
  from {
    stroke-dashoffset: 18;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
