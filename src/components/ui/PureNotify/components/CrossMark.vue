<template>
  <div class="cross-container">
    <svg viewBox="0 0 24 24" fill="none">
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="#F63F54"
        stroke-width="2"
        class="circle"
      />
      <path
        d="M8 8L16 16"
        stroke="#F63F54"
        stroke-width="2"
        stroke-linecap="round"
        class="cross-left"
      />
      <path
        d="M16 8L8 16"
        stroke="#F63F54"
        stroke-width="2"
        stroke-linecap="round"
        class="cross-right"
      />
    </svg>
  </div>
</template>

<script setup></script>

<style scoped lang="less">
/* prettier-ignore */
.cross-container {
  width: 24PX;
  height: 24PX;
  position: relative;

  .circle {
    stroke-dasharray: 76;
    stroke-dashoffset: 76;
    animation: circle-animation 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .cross-left, .cross-right {
    stroke-dasharray: 16;
    stroke-dashoffset: 16;
    animation: cross-animation 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards 0.4s;
  }
  
  // 右边的线延迟一点点，让动画更有层次
  .cross-right {
    animation-delay: 0.5s;
  }
}

@keyframes circle-animation {
  from {
    stroke-dashoffset: 76;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes cross-animation {
  from {
    stroke-dashoffset: 16;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
