<template>
  <div class="info-container">
    <svg viewBox="0 0 24 24" fill="none">
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="#3B82F6"
        stroke-width="2"
        class="circle"
      />
      <g class="info-symbol">
        <path
          d="M12 8L12 9"
          stroke="#3B82F6"
          stroke-width="2"
          stroke-linecap="round"
        />
        <path
          d="M12 11.5L12 16"
          stroke="#3B82F6"
          stroke-width="2"
          stroke-linecap="round"
        />
      </g>
    </svg>
  </div>
</template>

<script setup></script>

<style scoped lang="less">
/* prettier-ignore */
.info-container {
  width: 24PX;
  height: 24PX;
  position: relative;

  .circle {
    stroke-dasharray: 76;
    stroke-dashoffset: 76;
    animation: circle-animation 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .info-symbol {
    opacity: 0;
    animation: fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards 0.4s;
  }
}

@keyframes circle-animation {
  from {
    stroke-dashoffset: 76;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
