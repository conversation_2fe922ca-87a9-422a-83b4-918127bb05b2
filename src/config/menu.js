import { ROUTE_PATHS } from "@/constants/routes.js";
import { RTM_TYPES } from "@/utils/permissions.ts";

// ==================== 侧边栏菜单 ====================
export const menuItems = [
  {
    id: "dashboard",
    title: "首页",
    icon: "dashboard",
    path: ROUTE_PATHS.DASHBOARD,
    expanded: false,
    permissions: null, // 对所有RTM开放
  },
  {
    id: "forecast",
    title: "预警中心",
    icon: "warning-center",
    path: ROUTE_PATHS.FORECAST,
    expanded: false,
    permissions: [RTM_TYPES.MONO, RTM_TYPES.MULTI], // 只有Mono和Multi可以访问
    children: [
      {
        title: "激活率预警",
        path: ROUTE_PATHS.FORECAST_OPERATIONS,
        permissions: [RTM_TYPES.MONO, RTM_TYPES.MULTI], // 继承父级权限或单独设置
      },
      {
        title: "库存预警",
        path: ROUTE_PATHS.FORECAST_INVENTORY,
        // 不设置permissions，继承父级权限
      },
      {
        title: "销售预警",
        path: ROUTE_PATHS.FORECAST_ALERTS,
        permissions: [RTM_TYPES.MULTI], // 只有Multi可以访问
      },
      {
        title: "扫码预警",
        path: ROUTE_PATHS.FORECAST_MAINTENANCE,
        // 不设置permissions，继承父级权限
      },
      {
        title: "门店缺货",
        path: ROUTE_PATHS.FORECAST_PROJECTS,
        // 不设置permissions，继承父级权限
      },
      {
        title: "在仓库龄",
        path: ROUTE_PATHS.FORECAST_OPERATIONS_CARE,
        // 不设置permissions，继承父级权限
      },
      {
        title: "库存提醒",
        path: ROUTE_PATHS.FORECAST_ACTIVATION,
        // 不设置permissions，继承父级权限
      },
    ],
  },
  {
    id: "message",
    title: "消息中心",
    icon: "message-center",
    path: ROUTE_PATHS.MESSAGE,
    expanded: false,
    permissions: null, // 对所有RTM开放
  },
  {
    id: "operations",
    title: "运营中心",
    icon: "operation-center",
    path: ROUTE_PATHS.OPERATIONS,
    expanded: false,
    permissions: [RTM_TYPES.MONO, RTM_TYPES.MULTI], // 对所有RTM开放
    children: [
      {
        title: "运营异常",
        path: ROUTE_PATHS.OPERATIONS_DATA,
        permissions: [RTM_TYPES.MONO, RTM_TYPES.MULTI], // 示例权限设置
      },
      {
        title: "我的申报",
        path: ROUTE_PATHS.OPERATIONS_INVENTORY,
        // 不设置permissions，继承父级权限
      },
      {
        title: "备货计划",
        path: ROUTE_PATHS.OPERATIONS_PLAN,
        // 不设置permissions，继承父级权限
      },
      {
        title: "分货计划",
        path: ROUTE_PATHS.OPERATIONS_DISTRIBUTION,
        // 不设置permissions，继承父级权限
      },
      {
        title: "返点表现",
        path: ROUTE_PATHS.OPERATIONS_POINTS,
        // 不设置permissions，继承父级权限
      },
      {
        title: "运营评分",
        path: ROUTE_PATHS.OPERATIONS_SCORE,
        // 不设置permissions，继承父级权限
      },
      {
        title: "活动管理",
        path: ROUTE_PATHS.OPERATIONS_ACTIVITY,
        // 不设置permissions，继承父级权限
      },
      {
        title: "提升项目",
        path: ROUTE_PATHS.OPERATIONS_IMPROVEMENT,
        // 不设置permissions，继承父级权限
      },
    ],
  },
  {
    id: "data",
    title: "数据中心",
    icon: "data-center",
    path: ROUTE_PATHS.DATA,
    expanded: false,
    permissions: [RTM_TYPES.MULTI], // 只有Multi可以访问
    children: [
      {
        title: "销售表现",
        path: ROUTE_PATHS.DATA_SALES,
        // 不设置permissions，继承父级权限
      },
      {
        title: "库存表现",
        path: ROUTE_PATHS.DATA_INVENTORY,
        // 不设置permissions，继承父级权限
      },
      {
        title: "零售项目",
        path: ROUTE_PATHS.DATA_RETAIL,
        // 不设置permissions，继承父级权限
      },
      {
        title: "配件排行",
        path: ROUTE_PATHS.DATA_PARTS,
        // 不设置permissions，继承父级权限
      },
      {
        title: "门店客流",
        path: ROUTE_PATHS.DATA_TRAFFIC,
        // 不设置permissions，继承父级权限
      },
      {
        title: "SEED 平均等级",
        path: ROUTE_PATHS.DATA_SEED,
        // 不设置permissions，继承父级权限
      },
      {
        title: "DCOTA 点亮率",
        path: ROUTE_PATHS.DATA_DCOTA,
        // 不设置permissions，继承父级权限
      },
    ],
  },
];

/**
 * 根据路径查找菜单项
 * @param {string} path - 路径
 * @returns {Object|null} 匹配的菜单项
 */
export function findMenuItemByPath(path) {
  for (const item of menuItems) {
    if (item.path === path) {
      return item;
    }
    if (item.children) {
      const found = item.children.find((child) => child.path === path);
      if (found) {
        return { ...found, parent: item };
      }
    }
  }
  return null;
}

/**
 * 获取菜单面包屑
 * @param {string} path - 当前路径
 * @returns {Array} 面包屑数组
 */
export function getMenuBreadcrumb(path) {
  const breadcrumb = [];

  for (const item of menuItems) {
    if (item.path === path) {
      breadcrumb.push({ title: item.title, path: item.path });
      break;
    }
    if (item.children) {
      const found = item.children.find((child) => child.path === path);
      if (found) {
        breadcrumb.push({ title: item.title, path: item.path });
        breadcrumb.push({ title: found.title, path: found.path });
        break;
      }
    }
  }

  return breadcrumb;
}
