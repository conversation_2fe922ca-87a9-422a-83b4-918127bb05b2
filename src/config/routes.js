import { ROUTE_PATHS, ROUTE_NAMES } from "@/constants/routes.js";

// 路由和菜单解耦
const routes = [
  {
    path: ROUTE_PATHS.HOME,
    name: ROUTE_NAMES.HOME,
    component: () => import("@/views/DashboardView.vue"),
    meta: {
      title: "首页",
      requiresAuth: true,
      requiresLayout: true,
      icon: "dashboard",
      breadcrumb: "首页",
    },
  },
  {
    path: ROUTE_PATHS.DASHBOARD,
    name: ROUTE_NAMES.DASHBOARD,
    component: () => import("@/views/DashboardView.vue"),
    meta: {
      title: "首页",
      requiresAuth: false,
      requiresLayout: true,
      icon: "dashboard",
      breadcrumb: "首页",
    },
  },

  {
    path: ROUTE_PATHS.FORECAST,
    name: ROUTE_NAMES.FORECAST,
    component: () => import("@/views/ForecastView.vue"),
    meta: {
      title: "预警中心",
      requiresAuth: true,
      requiresLayout: true,
      icon: "warningCenter",
      breadcrumb: "预警中心",
    },
    children: [
      {
        path: "operations",
        name: ROUTE_NAMES.FORECAST_OPERATIONS,
        component: () => import("@/views/forecast/OperationsView.vue"),
        meta: {
          title: "激活率预警",
          breadcrumb: "激活率预警",
          parent: "预警中心",
        },
      },
      {
        path: "inventory",
        name: ROUTE_NAMES.FORECAST_INVENTORY,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "库存预警",
          breadcrumb: "库存预警",
          parent: "预警中心",
        },
      },
      {
        path: "alerts",
        name: ROUTE_NAMES.FORECAST_ALERTS,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "销售预警",
          breadcrumb: "销售预警",
          parent: "预警中心",
        },
      },
      {
        path: "maintenance",
        name: ROUTE_NAMES.FORECAST_MAINTENANCE,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "扫码预警",
          breadcrumb: "扫码预警",
          parent: "预警中心",
        },
      },
      {
        path: "projects",
        name: ROUTE_NAMES.FORECAST_PROJECTS,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "门店缺货",
          breadcrumb: "门店缺货",
          parent: "预警中心",
        },
      },
      {
        path: "operations-care",
        name: ROUTE_NAMES.FORECAST_OPERATIONS_CARE,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "在仓库龄",
          breadcrumb: "在仓库龄",
          parent: "预警中心",
        },
      },
      {
        path: "activation",
        name: ROUTE_NAMES.FORECAST_ACTIVATION,
        component: () => import("@/views/forecast/InventoryView.vue"),
        meta: {
          title: "库存提醒",
          breadcrumb: "库存提醒",
          parent: "预警中心",
        },
      },
    ],
  },

  {
    path: ROUTE_PATHS.MESSAGE,
    name: ROUTE_NAMES.MESSAGE,
    component: () => import("@/views/MessageView.vue"),
    meta: {
      title: "消息中心",
      requiresAuth: true,
      requiresLayout: true,
      icon: "messageCenter",
      breadcrumb: "消息中心",
    },
  },

  {
    path: ROUTE_PATHS.OPERATIONS,
    name: ROUTE_NAMES.OPERATIONS,
    component: () => import("@/views/DashboardView.vue"),
    meta: {
      title: "运营中心",
      requiresAuth: true,
      requiresLayout: true,
      icon: "operationCenter",
      breadcrumb: "运营中心",
    },
    children: [
      {
        path: "data",
        name: ROUTE_NAMES.OPERATIONS_DATA,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "运营异常",
          breadcrumb: "运营异常",
          parent: "运营中心",
        },
      },
      {
        path: "inventory",
        name: ROUTE_NAMES.OPERATIONS_INVENTORY,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "我的申报",
          breadcrumb: "我的申报",
          parent: "运营中心",
        },
      },

      {
        path: "plan",
        name: ROUTE_NAMES.OPERATIONS_PLAN,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "备货计划",
          breadcrumb: "备货计划",
          parent: "运营中心",
        },
      },
      {
        path: "distribution",
        name: ROUTE_NAMES.OPERATIONS_DISTRIBUTION,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "分货计划",
          breadcrumb: "分货计划",
          parent: "运营中心",
        },
      },
      {
        path: "points",
        name: ROUTE_NAMES.OPERATIONS_POINTS,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "返点表现",
          breadcrumb: "返点表现",
          parent: "运营中心",
        },
      },
      {
        path: "score",
        name: ROUTE_NAMES.OPERATIONS_SCORE,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "运营评分",
          breadcrumb: "运营评分",
          parent: "运营中心",
        },
      },
      {
        path: "activity",
        name: ROUTE_NAMES.OPERATIONS_ACTIVITY,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "活动管理",
          breadcrumb: "活动管理",
          parent: "运营中心",
        },
      },
      {
        path: "improvement",
        name: ROUTE_NAMES.OPERATIONS_IMPROVEMENT,
        component: () => import("@/views/operations/DataView.vue"),
        meta: {
          title: "提升项目",
          breadcrumb: "提升项目",
          parent: "运营中心",
        },
      },
    ],
  },

  {
    path: ROUTE_PATHS.DATA,
    name: ROUTE_NAMES.DATA,
    component: () => import("@/views/DataView.vue"),
    meta: {
      title: "数据中心",
      requiresAuth: true,
      requiresLayout: true,
      icon: "dataCenter",
      breadcrumb: "数据中心",
    },
    children: [
      {
        path: "sales",
        name: ROUTE_NAMES.DATA_SALES,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "销售表现",
          breadcrumb: "销售表现",
          parent: "数据中心",
        },
      },
      {
        path: "inventory",
        name: ROUTE_NAMES.DATA_INVENTORY,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "库存表现",
          breadcrumb: "库存表现",
          parent: "数据中心",
        },
      },
      {
        path: "retail",
        name: ROUTE_NAMES.DATA_RETAIL,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "零售项目",
          breadcrumb: "零售项目",
          parent: "数据中心",
        },
      },
      {
        path: "parts",
        name: ROUTE_NAMES.DATA_PARTS,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "配件排行",
          breadcrumb: "配件排行",
          parent: "数据中心",
        },
      },
      {
        path: "traffic",
        name: ROUTE_NAMES.DATA_TRAFFIC,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "门店客流",
          breadcrumb: "门店客流",
          parent: "数据中心",
        },
      },
      {
        path: "seed",
        name: ROUTE_NAMES.DATA_SEED,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "SEED 平均等级",
          breadcrumb: "SEED 平均等级",
          parent: "数据中心",
        },
      },
      {
        path: "dcota",
        name: ROUTE_NAMES.DATA_DCOTA,
        component: () => import("@/views/data/DataView.vue"),
        meta: {
          title: "DCOTA 点亮率",
          breadcrumb: "DCOTA 点亮率",
          parent: "数据中心",
        },
      },
    ],
  },

  // 认证中页面
  {
    path: ROUTE_PATHS.AUTHENTICATING,
    name: ROUTE_NAMES.AUTHENTICATING,
    component: () => import("@/views/AuthenticatingView.vue"),
    meta: {
      title: "权限验证中",
      requiresAuth: false,
      requiresLayout: false,
      hiddenInMenu: true,
    },
  },

  // 无权限访问页面
  {
    path: ROUTE_PATHS.NO_PERMISSION,
    name: ROUTE_NAMES.NO_PERMISSION,
    component: () => import("@/views/NoPermissionView.vue"),
    meta: {
      title: "访问受限",
      requiresAuth: false,
      requiresLayout: false,
      hiddenInMenu: true,
    },
  },

  // 404 页面
  {
    path: "/:pathMatch(.*)*",
    name: ROUTE_NAMES.NOT_FOUND,
    component: () => import("@/views/NotFoundView.vue"),
    meta: {
      title: "页面未找到",
      requiresAuth: false,
      requiresLayout: false,
      hiddenInMenu: true,
    },
  },
];

export default routes;
