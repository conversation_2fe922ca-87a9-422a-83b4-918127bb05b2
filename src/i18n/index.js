import { createI18n } from "vue-i18n";

import en from "./en.json";
import zh from "./zh.json";
import hk from "./hk.json";
import tw from "./tw.json";

export const i18n = createI18n({
  locale: "zh",
  fallbackLocale: "zh",
  messages: {
    en,
    zh,
    hk,
    tw,
  },
});

// 使用 import.meta.glob 预加载所有语言文件
const languageModules = import.meta.glob("./*.json");

// 语言加载函数
export async function loadLanguageAsync(lang) {
  // 如果语言包已经加载，直接切换
  if (i18n.global.availableLocales.includes(lang)) {
    return setI18nLanguage(lang);
  }

  // 动态导入语言包
  try {
    const modulePath = `./${lang}.json`;
    if (languageModules[modulePath]) {
      const messages = await languageModules[modulePath]();
      // 注册语言包
      i18n.global.setLocaleMessage(lang, messages.default);
      return setI18nLanguage(lang);
    } else {
      throw new Error(`Language file ${lang}.json not found`);
    }
  } catch (e) {
    console.error(`Could not load language ${lang}:`, e);
    return Promise.reject(e);
  }
}

// 设置语言
function setI18nLanguage(lang) {
  // 正确设置语言
  i18n.global.locale = lang;
  console.log("设置语言", lang);

  // 设置 html 的 lang 属性
  document.querySelector("html").setAttribute("lang", lang);
  // 保存到 sessionStorage
  sessionStorage.setItem("language", lang);
  return lang;
}

// 获取浏览器语言
export function getBrowserLanguage() {
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.indexOf("en") > -1) {
    return "en"; // 英文
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("cn") > -1) {
    return "zh"; // 简体中文
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("hk") > -1) {
    return "hk"; // 港繁
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("tw") > -1) {
    return "tw"; // 台繁
  } else {
    return "zh"; // 默认简体中文
  }
}

// 初始化语言
export async function setupLanguage() {
  const savedLang = sessionStorage.getItem("language");
  const browserLang = getBrowserLanguage();
  const lang = savedLang || browserLang || "zh";

  return await loadLanguageAsync(lang);
}
