// 产品排名接口
export interface ProductRankItem {
  rank: number;
  sub_lob: string;
  storsize_long_desc: string;
  color_long_desc: string;
  [key: string]: any; // 其它字段
}

export interface StoreRankItem {
  rank: number;
  metric: string;
  [key: string]: any; // 其它字段
}

export interface ProductRankData {
  lob: string;
  data: ProductRankItem[];
}

export interface StoreRankData {
  metric: string;
  ranks: StoreRankItem[];
}

export interface ProductRankResponse {
  code: number;
  data: ProductRankData[];
}

// format数据
export const formatProductRankData = (data: ProductRankData[]) => {
  const tabs = data.map((item) => ({
    label: item.lob,
    value: item.lob,
  }));

  const rankData = data.flatMap((item) =>
    (item.data || []).map((dataItem) => ({
      ...dataItem,
      lob: item.lob,
      name: item.lob,
      id: `${item.lob}-${dataItem.rank}`,
      title: dataItem.sub_lob,
      subTitle: `${dataItem.storsize_long_desc} ${dataItem.color_long_desc}`,
    }))
  );

  return {
    tabs,
    rankData,
  };
};

// format 门店评分
export const formatStoreRankData = (data: StoreRankData[]) => {
  const tabs = data.map((item) => ({
    label: item.metric,
    value: item.metric,
  }));

  const rankData = data.flatMap((item) =>
    (item.ranks || []).map((dataItem) => ({
      ...dataItem,
      lob: item.metric,
      name: item.metric,
      id: `${item.metric}-${dataItem.rank}`,
      title: dataItem.pos_name,
      subTitle: dataItem.pos_name,
    }))
  );

  return {
    tabs,
    rankData,
  };
};
