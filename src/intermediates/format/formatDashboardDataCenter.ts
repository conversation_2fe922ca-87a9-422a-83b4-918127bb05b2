import iPhoneIconUrl from "@/assets/icons/lob/iPhone.svg?url";
import iPadIconUrl from "@/assets/icons/lob/iPad.svg?url";
import MacIconUrl from "@/assets/icons/lob/Mac.svg?url";
import WatchIconUrl from "@/assets/icons/lob/Watch.svg?url";
import AirPodsIconUrl from "@/assets/icons/lob/AirPods.svg?url";
import Installment from "@/assets/icons/retail/installment.svg?url";
import NewMachineSetting from "@/assets/icons/retail/new-machine-settings.svg?url";
import PersonalTraining from "@/assets/icons/retail/personal-training.svg?url";
import TradeIn from "@/assets/icons/retail/trade-in.svg?url";
import {
  SalesPerformanceResponse,
  InventorySummaryResponse,
  RetailSummaryResponse,
} from "@/api/interface/DashboardDataCenter";
import { DmpDataCenterCard1x1ListItem, DmpDataCenterCard2x1ListItem } from "@dmp/components";

const lobIcons = {
  iPhone: iPhoneIconUrl,
  iPad: iPadIconUrl,
  Mac: MacIconUrl,
  Watch: WatchIconUrl,
  AirPods: AirPodsIconUrl,
};

const retailIcons = {
  installment: Installment,
  "new-machine-settings": NewMachineSetting,
  "personal-training": PersonalTraining,
  "trade-in": TradeIn,
};

// 销售表现
export const formatSalesPerformanceData = (data: SalesPerformanceResponse[]) => {
  return data.reduce((acc: DmpDataCenterCard2x1ListItem[], item) => {
    if (!item.lob || !item.weekly || !item.quarter) return acc;
    acc.push({
      name: item.lob,
      countUnitList: [
        {
          count: item.weekly,
          unit: "台",
          extra: "本周",
        },
        {
          count: item.quarter,
          unit: "台",
          extra: "季度",
        },
      ],
      icon: lobIcons[item.lob],
    });
    return acc;
  }, []);
};

// 库存表现
export const formatInventorySummaryData = (data: InventorySummaryResponse[]) => {
  return data.reduce((acc: DmpDataCenterCard1x1ListItem[], item) => {
    if (!item.lob || !item.inv) return acc;
    acc.push({
      name: item.lob,
      count: item.inv,
      unit: "台",
      icon: lobIcons[item.lob],
    });
    return acc;
  }, []);
};

// 零售项目
export const formatRetailSummaryData = (data: RetailSummaryResponse[]) => {
  return data.reduce((acc: DmpDataCenterCard1x1ListItem[], item) => {
    if (!item.lob || !item.inv) return acc;
    acc.push({
      name: item.lob,
      count: item.inv,
      unit: "台",
      icon: retailIcons[item.lob],
    });
    return acc;
  }, []);
};
