import {
  HealthDiagnosisResponse,
  FormattedHealthDiagnosis,
  FormattedAlertItem,
  HealthMetric,
} from "@/api/interface/DashboardHealthDiagnosis";

/**
 * 根据级别获取颜色和渐变
 */
function getLevelColors(level: string): {
  color?: string;
  gradient: string[];
  statusLevel: "normal" | "warning" | "good" | "qualified";
} {
  const gradientMap = {
    warning: ["#FF8175", "#F63F54"],
    normal: ["#FFE167", "#FF9500"],
    good: ["#2EB972", "#9DE76A"],
    qualified: ["#2EB972", "#9DE76A"],
  };

  return {
    color: level === "normal" ? "rgba(28,28,30,0.26)" : undefined,
    gradient: gradientMap[level as keyof typeof gradientMap] || ["#2EB972", "#9DE76A"],
    statusLevel: level as "normal" | "warning" | "good" | "qualified",
  };
}

/**
 * 根据分数和级别生成健康提示
 */
function generateHealthTips(level: string): string[] {
  const tips = {
    warning: "您的系統出現多個預警信號，需要重點關注",
    normal: "系統健康度良好，部分模塊需優化",
    good: "您的業務總體健康，個別指標需要關注",
  };
  return [tips?.[level] || ""];
}

function calculateProgress(
  warningCnt: number,
  maxWarningCnt: number
): {
  progress: number;
  warningCnt: number;
} {
  const healthPercentage = Math.max(0, (warningCnt / maxWarningCnt) * 100);
  return {
    progress: healthPercentage,
    warningCnt,
  };
}

/**
 * 格式化指标数据为警报状态项
 */
function formatMetricsToAlertItems(metrics: HealthMetric[]): FormattedAlertItem[] {
  const levelZhMap = {
    warning: "重点关注",
    normal: "需要关注",
    qualified: "基本正常",
    good: "继续保持",
  };
  return metrics.map((metric) => {
    const { color, gradient, statusLevel } = getLevelColors(metric.level);
    const progress = calculateProgress(metric.warning_cnt, metric.max_warning_cnt);
    return {
      id: metric.name,
      title: metric.cn_name,
      progress: progress.progress,
      warningCnt: progress.warningCnt,
      color,
      statusText: levelZhMap[metric.level as keyof typeof levelZhMap],
      gradient,
      statusLevel,
      showText: true,
      progressUnit: "次",
      textColor: metric.level === "normal" ? "rgba(28,28,30,0.56)" : gradient[1],
    };
  });
}

// 健康诊断数据格式化
export function formatHealthDiagnosisData(data: HealthDiagnosisResponse): FormattedHealthDiagnosis {
  const { summary, metrics } = data;

  return {
    progress: summary.score,
    statusText: "健康度",
    healthTips: generateHealthTips(summary.level, metrics),
    alertItems: formatMetricsToAlertItems(metrics),
    level: summary.level,
  };
}
