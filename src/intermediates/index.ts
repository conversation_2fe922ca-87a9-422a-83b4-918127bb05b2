import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { get } from "@/utils/http.ts";
import {
  FlexColsCard1x1x1Props,
  DmpDataCenterCard2x1Props,
  DmpDataCenterCard1x1Props,
  RankCardProps,
  SystemHealthIndicatorProps,
  TipCarouselProps,
  AlertStatusListProps,
  HealthDiagnosisProps,
} from "@dmp/components/src";
import { rand } from "@vueuse/core";
import { useDashboardOperationCenter } from "@/views/components/DashboardOperationCenter/composables/useDashboardOperationCenter";
import { useAppStore } from "@/stores";
import {
  SalesPerformanceResponse,
  InventorySummaryResponse,
  RetailSummaryResponse,
} from "@/api/interface/DashboardDataCenter";
import {
  formatSalesPerformanceData,
  formatInventorySummaryData,
  formatRetailSummaryData,
} from "./format/formatDashboardDataCenter";
import { formatProductRankData, formatStoreRankData } from "./format/formatDashOperationCenter";
import {
  HealthDiagnosisResponse,
  FormattedHealthDiagnosis,
} from "@/api/interface/DashboardHealthDiagnosis";
import { formatHealthDiagnosisData } from "./format/formatDashboardHealthDiagnosis";

const intermediatesManager = IntermediatesManager.instance;

const getMockData = (count = 1) =>
  Array(count)
    .fill(0)
    .map(() => ({
      icon: "",
      name: "Iphone",
      count: 50,
      unit: "台",
    }));

intermediatesManager.register(
  "dmp-flex-cols-card1x1x1",
  "/getImportantInfo",
  async (): Promise<Partial<FlexColsCard1x1x1Props>> => {
    return {
      data: getMockData(rand(1, 6)),
    };
  }
);

// 销售表现
intermediatesManager.register(
  "DmpDataCenterCard2x1",
  "/hk_tw/datacenter/sales_performance",
  async (): Promise<Partial<DmpDataCenterCard2x1Props>> => {
    const { code = 0, data = [] }: { code?: number; data: SalesPerformanceResponse[] } = await get(
      "/hk_tw/datacenter/sales_performance"
    );
    if (code !== 0) return { list: [] };
    return {
      list: formatSalesPerformanceData(data),
    };
  }
);

// 库存表现
intermediatesManager.register(
  "DmpDataCenterCard1x1",
  "/hk_tw/datacenter/inventory/summary",
  async (): Promise<Partial<DmpDataCenterCard1x1Props>> => {
    const { code = 0, data = [] }: { code?: number; data: InventorySummaryResponse[] } = await get(
      "/hk_tw/datacenter/inventory/summary"
    );
    if (code !== 0) return { list: [] };
    return {
      list: formatInventorySummaryData(data),
    };
  }
);

// 零售项目
intermediatesManager.register(
  "DmpDataCenterCard1x1",
  "/hk_tw/datacenter/retail_projects",
  async (): Promise<Partial<DmpDataCenterCard1x1Props>> => {
    // const { code = 0, data = [] }: { code?: number; data: RetailSummaryResponse[] } = await get(
    //   "/hk_tw/datacenter/retail_projects"
    // );
    // if (code !== 0) return { list: [] };
    // return {
    //   list: formatRetailSummaryData(data),
    // };
    return;
  }
);

intermediatesManager.register(
  "DmpRankCard",
  "/hk_tw/store_rank/show",
  async (): Promise<RankCardProps> => {
    try {
      const { code = 0, data = [] }: { code?: number; data: any[] } =
        await get("/hk_tw/store_rank/show");
      if (code === 0 && data.length > 0) {
        return formatStoreRankData(data);
      }
    } catch (error) {
      console.log(error);
    }

    // 返回默认值
    return {
      tabs: [],
      rankData: [],
    };
  }
);

intermediatesManager.register(
  "DmpRankCard",
  "/hk_tw/product_rank/show",
  async (): Promise<RankCardProps> => {
    try {
      const { code = 0, data = [] }: { code?: number; data: any[] } = await get(
        "/hk_tw/product_rank/show"
      );
      if (code === 0 && data.length > 0) {
        return formatProductRankData(data);
      }
    } catch (error) {
      console.log(error);
    }

    // 返回默认值
    return {
      tabs: [],
      rankData: [],
    };
  }
);

// 健康诊断
intermediatesManager.register(
  "DmpHealthDiagnosis",
  "/hk_tw/health/home_page",
  async (): Promise<Partial<HealthDiagnosisProps>> => {
    try {
      const { code = 0, data }: { code?: number; data: HealthDiagnosisResponse } = await get(
        "/hk_tw/health/home_page",
        {
          fiscal_week: "FY25Q4W1",
        }
      );

      if (code === 0 && data) {
        // 格式化 API 数据为组件需要的格式
        const formattedData = formatHealthDiagnosisData(data);
        return formattedData;
      }
    } catch (error) {
      console.error("获取健康诊断数据失败:", error);
    }
  }
);
