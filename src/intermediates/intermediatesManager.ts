type Translator<T> = () => Promise<T>;

export default class IntermediatesManager {
  static _instance: IntermediatesManager;

  translatorMap = new Map<string, Translator<any>>();

  static get instance() {
    if (!this._instance) {
      this._instance = new IntermediatesManager();
    }
    return this._instance;
  }

  getKey(compName: string, apiPath: string): string {
    return `${compName}:${apiPath}`;
  }

  register(compName: string, apiPath: string, translator: Translator<any>) {
    this.translatorMap.set(this.getKey(compName, apiPath), translator);
  }

  get(compName: string, apiPath: string) {
    return this.translatorMap.get(this.getKey(compName, apiPath));
  }
}
