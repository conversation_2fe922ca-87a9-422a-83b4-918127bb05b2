import { createApp } from "vue";
import pinia from "./stores/pinia";
import "./style.css";

import App from "./App.vue";
import router from "./router";
import initLogListener from "@/utils/initLogListener";
import "@/intermediates";
import "@dmp/components/dist/es/style.css";
import { i18n, setupLanguage } from "./i18n";

const app = createApp(App);

app.use(pinia);
app.use(router);
app.use(i18n);

// 初始化日志监听
initLogListener(router, app);

// 挂载应用
setupLanguage().then(() => {
  app.mount("#app");
});
