# 路由系统架构

## 📁 文件结构

```
src/
├── constants/
│   └── routes.js           # 路由常量定义
├── config/
│   └── menu.js            # 菜单配置和工具函数
├── router/
│   └── index.js           # 路由配置、实例和守卫
├── views/
│   ├── AuthenticatingView.vue    # 权限验证页面
│   ├── NoPermissionView.vue      # 无权限访问页面
│   └── NotFoundView.vue          # 404页面
└── App.vue                # 主应用组件（布局控制）
```

## 🎯 设计理念

- **关注点分离**：App.vue专注布局，路由守卫专注权限，页面专注业务
- **权限优先**：所有路由访问都经过权限验证
- **用户体验**：智能跳转，记住用户原始目标页面
- **布局灵活**：通过路由meta控制是否显示侧边栏等布局组件

## 🔄 权限验证流程

```mermaid
flowchart TD
    A[用户访问页面] --> B{系统已初始化?}
    B -->|否| C[跳转到认证页面]
    C --> D[显示权限验证UI]
    D --> E[后台验证权限]
    E --> F{验证结果}
    F -->|成功| G[跳转到原始目标页面]
    F -->|失败| H[跳转到无权限页面]
    B -->|是| I{用户有权限?}
    I -->|是| J[正常访问]
    I -->|否| H
```

## 📖 核心文件说明

### `constants/routes.js`

- **职责**：定义所有路由名称和路径常量
- **新增**：`AUTHENTICATING` 认证中页面常量

```javascript
export const ROUTE_NAMES = {
  HOME: "home",
  AUTHENTICATING: "authenticating",
  NO_PERMISSION: "no-permission",
  NOT_FOUND: "NotFound",
  // ... 其他路由
};
```

### `router/index.js`

- **职责**：路由配置、权限守卫、布局控制
- **特性**：
  - 直接使用动态import（移除lazy-load依赖）
  - 智能权限验证和跳转
  - 支持记录并跳转到原始目标页面

```javascript
// 路由配置示例
{
  path: ROUTE_PATHS.HOME,
  component: () => import("@/views/DashboardView.vue"),
  meta: {
    requiresAuth: true,      // 需要权限验证
    requiresLayout: true,    // 需要显示布局（侧边栏等）
    title: "首页",
  },
}

// 特殊页面配置
{
  path: ROUTE_PATHS.AUTHENTICATING,
  component: () => import("@/views/AuthenticatingView.vue"),
  meta: {
    requiresAuth: false,     // 不需要权限验证
    requiresLayout: false,   // 全屏显示，不显示布局
  },
}
```

### `App.vue`

- **职责**：根据路由meta控制布局显示
- **逻辑**：简化为布局控制，移除权限判断

```vue
<template>
  <!-- 需要布局的页面：显示侧边栏等 -->
  <template v-if="needsLayout">
    <Sidebar />
    <main><RouterView /></main>
  </template>

  <!-- 不需要布局的页面：全屏显示 -->
  <template v-else>
    <RouterView />
  </template>
</template>

<script setup>
const needsLayout = computed(() => route.meta?.requiresLayout !== false);
</script>
```

## 🛡️ 权限验证系统

### 路由守卫逻辑

```javascript
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();

  // 1. 系统初始化（仅执行一次）
  if (!systemInitialized) {
    userStore.initializeApp(router); // 异步执行，不阻塞
    systemInitialized = true;
  }

  // 2. 根据权限状态进行路由控制
  if (userStore.isInitializing) {
    // 初始化中：跳转到认证页面，记录原始目标
    next({
      name: "AUTHENTICATING",
      query: { redirect: to.fullPath },
    });
  } else if (userStore.hasAppPermission === false) {
    // 无权限：跳转到无权限页面
    next({ name: "NO_PERMISSION" });
  } else {
    // 有权限：正常访问
    next();
  }
});
```

### 智能跳转机制

权限验证成功后，系统会：

1. 读取认证页面的 `redirect` 查询参数
2. 跳转到用户原本想访问的页面
3. 如果没有原始目标，则跳转到首页

```javascript
// 在 initializeApp 中
const redirectPath = currentRoute.query?.redirect;
if (redirectPath && redirectPath !== "/authenticating") {
  router.replace(redirectPath); // 跳转到原始目标
} else {
  router.replace({ name: "HOME" }); // 默认跳转首页
}
```

## 🎨 布局控制系统

### Meta配置

每个路由通过meta字段控制其行为：

```javascript
meta: {
  title: "页面标题",
  requiresAuth: true,      // 是否需要权限验证
  requiresLayout: true,    // 是否需要显示布局
  icon: "dashboard",       // 菜单图标
  breadcrumb: "面包屑",
  hiddenInMenu: false,     // 是否在菜单中隐藏
}
```

### 页面类型分类

| 页面类型 | requiresLayout | requiresAuth | 说明                   |
| -------- | -------------- | ------------ | ---------------------- |
| 业务页面 | `true`         | `true`       | 显示完整布局，需要权限 |
| 认证页面 | `false`        | `false`      | 全屏显示，不需要权限   |
| 错误页面 | `false`        | `false`      | 全屏显示，不需要权限   |

## 🚀 使用指南

### 1. 添加新的业务页面

```javascript
// 1. 在 constants/routes.js 中添加常量
export const ROUTE_NAMES = {
  NEW_PAGE: "new-page",
};

// 2. 在 router/index.js 中添加路由
{
  path: "/new-page",
  name: ROUTE_NAMES.NEW_PAGE,
  component: () => import("@/views/NewPageView.vue"),
  meta: {
    title: "新页面",
    requiresAuth: true,
    requiresLayout: true,
  },
}
```

### 2. 添加特殊页面（如错误页面）

```javascript
{
  path: "/special-page",
  name: "SPECIAL_PAGE",
  component: () => import("@/views/SpecialPageView.vue"),
  meta: {
    title: "特殊页面",
    requiresAuth: false,    // 不需要权限
    requiresLayout: false,  // 全屏显示
    hiddenInMenu: true,     // 不在菜单显示
  },
}
```

### 3. 权限验证调试

系统提供详细的控制台日志：

```
🚀 系统开始初始化...
🔄 系统初始化中，跳转到认证页面
✅ 系统初始化完成，用户有权限
🔄 权限验证成功，跳转到原始目标页面: /forecast
✅ 路由守卫通过，正常跳转
```

## 🔧 技术特性

### 性能优化

- **代码分割**：直接使用动态import，Vite自动优化
- **非阻塞初始化**：权限验证不阻塞路由跳转
- **智能缓存**：权限状态缓存，避免重复验证

### 用户体验

- **记忆功能**：记住用户原始目标页面
- **实时反馈**：认证页面显示目标页面信息
- **流畅跳转**：验证完成后立即跳转，无延迟

### 开发体验

- **类型安全**：路由常量避免拼写错误
- **模块化**：各文件职责明确，易于维护
- **调试友好**：详细的控制台日志

## 🎯 最佳实践

1. **始终使用路由常量**：避免硬编码路径
2. **合理设置meta**：明确页面的权限和布局需求
3. **权限优先**：新页面默认需要权限验证
4. **布局一致**：业务页面保持统一的布局体验
5. **错误处理**：为特殊情况提供友好的错误页面

## 🚧 注意事项

- 认证页面不应包含业务逻辑，只负责UI显示
- 权限验证逻辑集中在store和路由守卫中
- 避免在组件中直接处理路由跳转
- 新增路由时注意meta配置的完整性
