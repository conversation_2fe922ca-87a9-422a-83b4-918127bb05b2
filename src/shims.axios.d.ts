import axios, { AxiosResponse } from "axios";

declare module "axios" {
  // 定义通用的响应结构
  export interface Result<T> {
    code: number;
    message?: string;
    data: T;
    msg?: string;
  }

  export interface AxiosResponse<T = any> {
    data: Result<T>;
  }

  // 扩展 Axios 实例类型
  export interface AxiosInstance {
    request<T = any>(config: AxiosRequestConfig): Promise<Result<T>>;
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<Result<T>>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<Result<T>>;
    head<T = any>(url: string, config?: AxiosRequestConfig): Promise<Result<T>>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<Result<T>>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<Result<T>>;
    patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<Result<T>>;
  }
}

declare global {
  interface Error {
    isPermissionError?: boolean;
    code?: number;
  }
}
