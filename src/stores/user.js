import { getUserInfo, getResellerInfo } from "@/api";
import { ROUTE_NAMES } from "@/constants/routes.js";
import {
  hasPermission,
  hasRoutePermission,
  filterMenuByPermissions,
} from "@/utils/permissions.ts";

export const useUserStore = defineStore("user", () => {
  const userInfo = ref(null);
  const resellerInfo = ref(null);
  const isAuthenticated = ref(false);
  const isResellerAuthenticated = ref(false);
  const hasAppPermission = ref(null); //三态设计 null: 未初始化, true: 有权限, false: 无权限
  const isLoading = ref(false);
  const isInitializing = ref(true); // 系统初始化状态
  const authCredentials = ref(null); // 用于存储从 sessionStorage 读取的必要信息

  // 获取必要信息
  function loadCredentialsFromSession() {
    const resellerId = sessionStorage.getItem("reseller-id");
    const resellerRtm = sessionStorage.getItem("reseller-rtm");

    if (resellerId && resellerRtm) {
      authCredentials.value = {
        resellerId,
        resellerRtm,
        shieldDsPrsId: sessionStorage.getItem("shield-ds-prsId"),
        shieldDsPrsTypeCode: sessionStorage.getItem("shield-ds-prsTypeCode"),
        userType: sessionStorage.getItem("user-type"),
      };
      console.log("✅ 必要信息已从 sessionStorage 获取到 并且存储到 store");
    } else {
      authCredentials.value = null;
    }
  }

  // 检查 store 中是否有权限数据
  function hasCredentialsInStore() {
    return !!authCredentials.value;
  }

  // 清除所有权限数据和状态
  function clearAllPermissions() {
    // 清除状态
    userInfo.value = null;
    resellerInfo.value = null;
    isAuthenticated.value = false;
    isResellerAuthenticated.value = false;
    hasAppPermission.value = false;
    isInitializing.value = false; // 初始化完成
    authCredentials.value = null; // 清除凭证

    // 清除 sessionStorage (暂时注释掉，根据需要启用)
    // sessionStorage.removeItem("reseller-id");
    // sessionStorage.removeItem("reseller-rtm");
    // sessionStorage.removeItem("shield-ds-prsId");
    // sessionStorage.removeItem("shield-ds-prsTypeCode");
    // sessionStorage.removeItem("user-type");

    console.log("🧹 已清除所有权限数据");
  }

  // 权限检查并获取用户信息
  async function checkPermissionAndFetchUser() {
    if (!hasCredentialsInStore()) {
      console.log("🚫 Store 中权限凭证不存在");
      hasAppPermission.value = false;
      isInitializing.value = false;
      return { success: false, reason: "no-credentials-in-store" };
    }

    console.log("🔍 Store 中权限凭证存在，开始验证用户信息");
    isLoading.value = true;

    try {
      const res = await getUserInfo();
      const { code, data } = res;

      if (code === 0) {
        userInfo.value = data;
        isAuthenticated.value = true;
        hasAppPermission.value = true;
        isInitializing.value = false;
        console.log("✅ 用户验证成功，获得应用权限");
        return { success: true, data };
      } else {
        console.warn("❌ 用户验证失败:", data?.message);
        hasAppPermission.value = false;
        isInitializing.value = false;
        return {
          success: false,
          reason: "user-auth-failed",
          message: data?.message,
        };
      }
    } catch (error) {
      console.error("❌ 用户信息请求失败:", error);
      if (error.isPermissionError) {
        hasAppPermission.value = false;
        isInitializing.value = false;
        return {
          success: false,
          reason: "permission-error",
          message: error.message,
        };
      }
      hasAppPermission.value = false;
      isInitializing.value = false;
      return {
        success: false,
        reason: "network-error",
        message: error.message,
      };
    } finally {
      isLoading.value = false;
    }
  }

  // 获取经销商信息
  async function fetchResellerInfo() {
    if (!hasAppPermission.value) {
      console.warn("🚫 没有应用权限，无法获取经销商信息");
      return { success: false, reason: "no-permission" };
    }

    isLoading.value = true;
    try {
      const res = await getResellerInfo();
      const { code, data } = res;

      if (code === 0) {
        resellerInfo.value = data;
        isResellerAuthenticated.value = true;
        console.log("✅ 经销商信息获取成功");
        return { success: true, data };
      } else {
        console.warn("❌ 经销商信息获取失败:", data?.message);
        notify.error(data?.message || "获取经销商信息失败");
        return { success: false, error: data?.message };
      }
    } catch (error) {
      console.error("❌ 经销商信息请求失败:", error);
      if (error.isPermissionError) {
        return {
          success: false,
          reason: "permission-error",
          message: error.message,
        };
      }
      notify.error(error.message || "网络错误，请稍后重试");
      return { success: false, error: error.message };
    } finally {
      isLoading.value = false;
    }
  }

  // todo 需要确认 user 和reseller 都获取成功后，再跳转 还是分开跳转 如果是user 获取成功就跳转则需要解决埋点内 resellerinfo为空的问题
  // todo 因为页面渲染就会曝光， 这个时候resellerinfo还没请求
  async function initializeApp(router) {
    console.log("🚀 开始初始化应用...");
    isInitializing.value = true;

    // 首先从 sessionStorage 加载必要信息到 store
    loadCredentialsFromSession();

    const result = await checkPermissionAndFetchUser();

    if (result.success) {
      console.log("✅ 用户验证成功，开始获取经销商信息...");

      // 自动获取经销商信息
      const resellerResult = await fetchResellerInfo();

      if (resellerResult.success) {
        console.log("✅ 应用初始化完全成功（包含经销商信息）");
      } else {
        console.log(
          "⚠️ 应用初始化成功，但经销商信息获取失败:",
          resellerResult.reason,
        );
      }

      // 权限验证成功，跳转到原始目标路由或首页
      if (router) {
        // 获取原始目标路由（从认证页面的查询参数中读取）
        const currentRoute = router.currentRoute.value;
        const redirectPath = currentRoute.query?.redirect;

        if (redirectPath && redirectPath !== "/authenticating") {
          console.log("🔄 权限验证成功，跳转到原始目标页面:", redirectPath);
          router.replace(redirectPath || { name: ROUTE_NAMES.HOME });
        } else {
          console.log("🔄 权限验证成功，跳转到首页");
          router.replace({ name: ROUTE_NAMES.HOME });
        }
      }

      return { success: true };
    } else {
      console.log("❌ 应用初始化失败:", result.reason);

      // 权限验证失败，跳转到无权限页面
      if (router) {
        console.log("🔄 权限验证失败，跳转到无权限页面");
        router.replace({ name: ROUTE_NAMES.NO_PERMISSION });
      }

      return result;
    }
  }

  // ==================== 权限相关功能 ====================

  // 获取当前用户的RTM值
  const userRtm = computed(() => {
    return resellerInfo.value?.rtm || null;
  });

  // 检查用户是否有指定权限
  function checkPermission(requiredPermissions) {
    return hasPermission(userRtm.value, requiredPermissions);
  }

  // 检查用户是否有访问指定路由的权限
  function checkRoutePermission(route, allRoutes = []) {
    return hasRoutePermission(userRtm.value, route, allRoutes);
  }

  // 过滤菜单项，只保留用户有权限的菜单
  function filterMenuItems(menuItems) {
    return filterMenuByPermissions(menuItems, userRtm.value);
  }

  return {
    // state
    userInfo,
    resellerInfo,
    isAuthenticated,
    isResellerAuthenticated,
    hasAppPermission,
    isLoading,
    isInitializing,
    authCredentials, // 暴露凭证给 http.ts 使用

    // method
    clearAllPermissions,
    checkPermissionAndFetchUser,
    fetchResellerInfo,
    initializeApp,

    // permissions
    userRtm,
    checkPermission,
    checkRoutePermission,
    filterMenuItems,
  };
});
