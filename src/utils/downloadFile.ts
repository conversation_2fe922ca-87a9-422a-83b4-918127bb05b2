type Res = {
  headers: Record<string, string>;
  data: Blob;
};
export function downloadFile(res: Res): void {
  // 优先读取additional-attr-by-file-name字段，其次读取content-disposition字段
  const fileName =
    res.headers["additional-attr-by-file-name"] ||
    window.decodeURI(res.headers["content-disposition"].split("=")[1]);
  const _filename = fileName.replace(/['"]/g, "");
  const aLink = document.createElement("a");
  const url = window.URL.createObjectURL(res.data);
  aLink.href = url;
  aLink.setAttribute("download", _filename); // 设置下载文件名称
  document.body.appendChild(aLink);
  aLink.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(aLink);
}
