// 获取操作系统信息
export function getOsInfo() {
  const userAgent = navigator.userAgent.toLowerCase();
  let name = "Unknown";
  let version = "Unknown";
  if (userAgent.indexOf("win") > -1) {
    name = "Windows";
    const reg = /nt [\d._]+/gi;
    const v_info = userAgent.match(reg);
    version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, "."); //得到版本号9.3.2或者9.0
  } else if (userAgent.indexOf("iphone") > -1) {
    name = "iPhone";
    const reg = /os [\d._]+/gi;
    const v_info = userAgent.match(reg);
    version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, "."); //得到版本号9.3.2或者9.0
  } else if (userAgent.indexOf("mac") > -1) {
    name = "Mac";
    const reg = /x [\d._]+/gi;
    const v_info = userAgent.match(reg);
    version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, "."); //得到版本号9.3.2或者9.0
  } else if (
    userAgent.indexOf("x11") > -1 ||
    userAgent.indexOf("unix") > -1 ||
    userAgent.indexOf("sunname") > -1 ||
    userAgent.indexOf("bsd") > -1
  ) {
    name = "Unix";
  } else if (userAgent.indexOf("linux") > -1) {
    if (userAgent.indexOf("android") > -1) {
      name = "Android";
      const reg = /android [\d._]+/gi;
      const v_info = userAgent.match(reg);
      version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, "."); //得到版本号4.2.2
    } else {
      name = "Linux";
    }
  } else {
    name = "Unknown";
  }
  return { name, version };
}

export function getBrowser() {
  const userAgent = navigator.userAgent; //取得浏览器的userAgent字符串

  const isIE11 = userAgent.toLowerCase().match(/rv:([\d.]+)\) like gecko/); // IE 11中userAgent已经不包含'msie'所以用'msie'不能判断IE 11
  const isOpera = userAgent.indexOf("Opera") > -1; //判断是否Opera浏览器
  const isIE =
    (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) || isIE11; //判断是否IE浏览器
  const isEdge = userAgent.indexOf("Edge") > -1; //判断是否IE的Edge浏览器
  const isFF = userAgent.indexOf("Firefox") > -1; //判断是否Firefox浏览器
  const isSafari = userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") === -1; //判断是否Safari浏览器
  const isChrome = userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Safari") > -1; //判断Chrome浏览器

  if (isIE) {
    return "IE";
  } //IE版本过低

  if (isOpera) {
    return "Opera";
  }
  if (isEdge) {
    return "Edge";
  }
  if (isFF) {
    return "Firefox";
  }
  if (isSafari) {
    return "Safari";
  }
  if (isChrome) {
    return "Chrome";
  }
  return "--";
}

export default {
  os: getOsInfo(),
  browser: getBrowser(),
};
