import axios from "axios";
import { useUserStore } from "@/stores/user.js";

// 从环境变量获取配置
const apiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || "/mybusiness",
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  enableConsoleLog: import.meta.env.VITE_ENABLE_CONSOLE_LOG === "true",
  showDebugInfo: import.meta.env.VITE_SHOW_DEBUG_INFO === "true",
  uploadMaxSize: parseInt(import.meta.env.VITE_UPLOAD_MAX_SIZE) || 10485760,
};

// 权限错误处理函数
function handlePermissionError(message = "NO PERMISSION") {
  console.warn("🚫 HTTP拦截器检测到权限错误:", message);

  const userStore = useUserStore();
  userStore.clearAllPermissions();
}

const http = axios.create({
  baseURL: apiConfig.baseURL, // 基础 URL
  timeout: apiConfig.timeout, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    const credentials = userStore.authCredentials; // 从 store 获取凭证

    if (credentials) {
      // 将凭证附加到请求头 必要步骤 否则请求会失败
      config.headers["reseller-id"] = credentials.resellerId;
      config.headers["reseller-rtm"] = credentials.resellerRtm;
      config.headers["shield-ds-prsId"] = credentials.shieldDsPrsId;
      config.headers["shield-ds-prsTypeCode"] = credentials.shieldDsPrsTypeCode;
      config.headers["user-type"] = credentials.userType;
    }

    if (apiConfig.enableConsoleLog) {
      console.log("🚀 发送请求：", config.method?.toUpperCase(), config.url);
      console.log("📋 请求头2：", config.headers);
      if (apiConfig.showDebugInfo) {
        console.log("🔍 完整配置：", config);
      }
    }

    return config;
  },
  (error) => {
    if (apiConfig.enableConsoleLog) {
      console.error("❌ 请求错误：", error);
    }
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    const { data } = response;

    // todo: 还有别的code 需要对齐
    if (data.code === 10011) {
      handlePermissionError(data?.message || "NO PERMISSION");
      const error = new Error(data?.message || "NO PERMISSION");
      error.isPermissionError = true;
      error.code = data.code;
      return Promise.reject(error);
    }

    if (data && typeof data === "object") {
      // 根据后端约定的状态码处理
      if (data.code === 0) {
        // 成功情况不自动显示消息，由具体业务决定
        return data; // 返回实际数据
      } else {
        // 业务错误
        const message = data.message || "请求失败";
        notify.error(message);
        return Promise.reject(new Error(message));
      }
    }

    // 直接返回数据（如果后端直接返回数据不包装）
    return data;
  },
  (error) => {
    if (apiConfig.enableConsoleLog) {
      console.error("❌ 响应错误：", error.message);
      if (error.response) {
        console.error("📨 错误响应：", {
          status: error.response.status,
          statusText: error.response.statusText,
          url: error.config?.url,
          method: error.config?.method?.toUpperCase(),
          data: error.response.data,
        });
      } else if (error.request) {
        console.error("📤 请求失败：", error.request);
      }
    }

    if (error.response) {
      const { status, data } = error.response;

      // todo: 临时方案需要修改 需要约定错误码
      if (data && data.code === 10011) {
        handlePermissionError(data.message);
        const permissionError = new Error(data.message || "NO PERMISSION");
        permissionError.isPermissionError = true;
        permissionError.code = data.code;
        return Promise.reject(permissionError);
      }

      switch (status) {
        case 401:
          // HTTP 401 权限问题
          handlePermissionError("访问权限不足");
          break;

        case 403:
          notify.error("禁止访问");
          break;

        case 404:
          notify.error("请求的资源不存在");
          break;

        case 500:
          notify.error("服务器内部错误");
          break;

        case 502:
        case 503:
        case 504:
          notify.error("服务器暂时不可用，请稍后重试");
          break;

        default:
          notify.error(data?.message || error.message || "网络错误");
      }
    } else if (error.request) {
      // 网络错误
      notify.error("网络连接失败，请检查网络设置");
    } else {
      // 其他错误
      notify.error(error.message || "发生未知错误");
    }

    return Promise.reject(error);
  }
);

// 常用请求方法

// 通用请求方法
export const request = (config) => {
  return http(config);
};

export const get = <T>(url: string, params = {}) => {
  return http.get<T>(url, { params });
};

export const post = <T>(url: string, data = {}) => {
  return http.post<T>(url, data);
};

export const put = <T>(url: string, data = {}) => {
  return http.put<T>(url, data);
};

export const del = <T>(url: string, params = {}) => {
  return http.delete<T>(url, { params });
};

export { apiConfig };

export default http;
