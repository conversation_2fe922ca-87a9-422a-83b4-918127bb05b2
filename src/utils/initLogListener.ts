import {
  addListener,
  removeListener,
  updatedListener,
  beforeEach,
} from "./v-log";

function initLogListener(router, app) {
  router.beforeEach((to, from, next) => {
    beforeEach(to, from, next);
  });
  app.directive("log", {
    mounted(el, binding) {
      addListener(el, binding);
    },
    updated(el, binding) {
      updatedListener(el, binding);
    },
    unmounted(el, binding) {
      removeListener(el, binding);
    },
  });
}
export default initLogListener;
