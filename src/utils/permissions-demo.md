# RTM权限系统使用指南

## 系统概述

本权限系统基于用户的RTM（Reseller Type Management）值来控制路由和菜单的访问权限。

## 权限配置

### RTM类型

- `Mono`: 单一经销商
- `Multi`: 多元经销商
- `Carrier`: 运营商

### 权限配置方式

1. **路由权限**: 在路由的`meta.permissions`字段配置
2. **菜单权限**: 在菜单配置的`permissions`字段配置
3. **权限继承**: 子路由/子菜单不设置权限时自动继承父级权限
4. **开放权限**: 设置为`null`时对所有RTM开放

## 使用示例

### 路由配置

```javascript
{
  path: '/forecast',
  name: 'forecast',
  meta: {
    permissions: ['Mono', 'Multi'], // 只有Mono和Multi可访问
  },
  children: [
    {
      path: 'alerts',
      name: 'forecast-alerts',
      meta: {
        permissions: ['Multi'], // 只有Multi可访问
      }
    },
    {
      path: 'inventory',
      name: 'forecast-inventory',
      meta: {
        // 不设置权限，继承父路由权限 ['Mono', 'Multi']
      }
    }
  ]
}
```

### 菜单配置

```javascript
{
  id: 'data',
  title: '数据中心',
  permissions: ['Multi'], // 只有Multi可访问
  children: [
    {
      title: '销售表现',
      // 继承父级权限
    },
    {
      title: '特殊报表',
      permissions: ['Multi', 'Carrier'], // 单独设置权限
    }
  ]
}
```

## 权限检查方法

### 在组件中使用

```javascript
import { useUserStore } from "@/stores/user.js";

const userStore = useUserStore();

// 检查用户RTM
console.log("用户RTM:", userStore.userRtm);

// 检查特定权限
const hasPermission = userStore.checkPermission(["Mono", "Multi"]);

// 检查路由权限
const hasRoutePermission = userStore.checkRoutePermission(route);
```

## 测试方法

### 1. 修改SessionStorage中的RTM值

```javascript
// 在浏览器控制台中执行
sessionStorage.setItem("reseller-rtm", "Mono");
location.reload();
```

### 2. 观察菜单变化

- RTM为`Mono`时：可以看到首页、预警中心、消息中心、运营中心
- RTM为`Multi`时：可以看到所有菜单包括数据中心
- RTM为`Carrier`时：根据配置显示对应菜单

### 3. 测试路由访问

直接访问URL，系统会检查权限：

- 有权限：正常显示页面
- 无权限：跳转到无权限页面

## 当前权限配置示例

### 菜单权限配置

- **首页**: 对所有RTM开放
- **预警中心**: 只有Mono和Multi可访问
  - 销售预警: 只有Multi可访问
  - 其他子菜单: 继承父级权限
- **消息中心**: 对所有RTM开放
- **运营中心**: 对所有RTM开放
  - 运营异常: 只有Mono和Multi可访问
  - 其他子菜单: 继承父级权限
- **数据中心**: 只有Multi可访问

## 注意事项

1. 权限检查在路由守卫中进行，确保URL直接访问也会被拦截
2. 菜单会根据权限自动过滤，用户看不到无权限的菜单项
3. 子路由/子菜单的权限继承机制确保了权限的一致性
4. 系统会等待用户RTM信息加载完成后再显示菜单，避免权限闪烁

## 扩展方法

如需添加新的RTM类型，请：

1. 在`src/utils/permissions.ts`中的`RTM_TYPES`添加新类型
2. 在路由和菜单配置中使用新的RTM类型
3. 更新显示名称映射
