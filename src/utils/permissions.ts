/**
 * 权限工具函数
 * 用于处理基于RTM值的权限检查和路由权限继承
 */

/**
 * 检查用户是否有访问指定权限的权限
 * @param {string} userRtm - 用户的RTM值
 * @param {Array|null} requiredPermissions - 需要的权限数组，null表示对所有人开放
 * @returns {boolean} 是否有权限
 */
export function hasPermission(userRtm, requiredPermissions) {
  // 如果权限为null，表示对所有人开放
  if (requiredPermissions === null || requiredPermissions === undefined) {
    return true;
  }

  // 如果没有设置权限数组或为空数组，默认对所有人开放
  if (!Array.isArray(requiredPermissions) || requiredPermissions.length === 0) {
    return true;
  }

  // 如果用户RTM为空，无权限
  if (!userRtm) {
    return false;
  }

  // 检查用户RTM是否在权限数组中
  return requiredPermissions.includes(userRtm);
}

/**
 * 获取路由的有效权限（处理子路由继承）
 * @param {Object} route - 路由对象
 * @param {Array} allRoutes - 所有路由数组
 * @returns {Array|null} 有效权限数组
 */
export function getRoutePermissions(route, allRoutes = []) {
  // 如果路由本身有权限设置，直接返回
  if (route.meta && route.meta.permissions !== undefined) {
    return route.meta.permissions;
  }

  // 如果是子路由，查找父路由的权限
  if (route.path && route.path.includes("/")) {
    const pathSegments = route.path.split("/").filter(Boolean);

    // 逐级向上查找父路由
    for (let i = pathSegments.length - 1; i >= 0; i--) {
      const parentPath = "/" + pathSegments.slice(0, i).join("/");
      const parentRoute = allRoutes.find((r) => r.path === parentPath);

      if (parentRoute && parentRoute.meta && parentRoute.meta.permissions !== undefined) {
        return parentRoute.meta.permissions;
      }
    }
  }

  // 如果没有找到任何权限设置，默认返回null（对所有人开放）
  return null;
}

/**
 * 检查用户是否有访问指定路由的权限
 * @param {string} userRtm - 用户的RTM值
 * @param {Object} route - 路由对象
 * @param {Array} allRoutes - 所有路由数组
 * @returns {boolean} 是否有权限
 */
export function hasRoutePermission(userRtm, route, allRoutes = []) {
  const permissions = getRoutePermissions(route, allRoutes);
  return hasPermission(userRtm, permissions);
}

/**
 * 过滤菜单项，只保留用户有权限的菜单
 * @param {Array} menuItems - 菜单项数组
 * @param {string} userRtm - 用户的RTM值
 * @returns {Array} 过滤后的菜单项
 */
export function filterMenuByPermissions(menuItems, userRtm) {
  return menuItems
    .filter((item) => {
      // 检查主菜单项权限
      return hasPermission(userRtm, item.permissions);
    })
    .map((item) => {
      // 如果有子菜单，过滤子菜单
      if (item.children && Array.isArray(item.children)) {
        const filteredChildren = item.children.filter((child) => {
          // 子菜单权限继承：如果子菜单没有设置权限，使用父菜单权限
          const childPermissions =
            child.permissions !== undefined ? child.permissions : item.permissions;
          return hasPermission(userRtm, childPermissions);
        });

        // 如果过滤后子菜单为空，返回null（稍后过滤掉）
        if (filteredChildren.length === 0) {
          return null;
        }

        // 返回包含过滤后子菜单的新对象
        return {
          ...item,
          children: filteredChildren,
        };
      }

      // 没有子菜单，直接返回原项
      return item;
    })
    .filter((item) => item !== null); // 过滤掉没有子菜单的父菜单项
}

/**
 * 获取所有RTM类型的常量
 */
export const RTM_TYPES = {
  MONO: "Mono",
  MULTI: "MultiBrand",
  CARRIER: "Carrier",
  // 可以在这里添加更多RTM类型
};
