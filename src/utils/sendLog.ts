import router from "@/router";
import clientInfo from "./getClientInfo.js";
import { camelCaseToLine, isEmptyObj, toString } from "./util";
import _package from "../../package.json";
import { v4 } from "uuid";
import pinia from "@/stores/pinia";
import { useUserStore } from "@/stores/user";
import axios from "axios";

const REPORTURL = "/frontend_report/api/v1.0/event_tracking";

const getUserStore = () => useUserStore(pinia);

const getUUID = () => {
  const localUUID = localStorage.getItem("myBusiness_UUID");
  if (localUUID) {
    return localUUID;
  } else {
    const v4UUID = v4();
    localStorage.setItem("myBusiness_UUID", v4UUID);
    return v4UUID;
  }
};
const getPrsId = () => {
  const userStore = getUserStore();
  const prsId = userStore.userInfo?.prs_id;
  if (!prsId) return "";
  return btoa(String(prsId));
};

// 公共参数
export const commonParams = () => {
  const userStore = getUserStore();
  return {
    uuid: getUUID(),
    app_name: "my-business-v2",
    os: `${clientInfo.os.name}_${clientInfo.os.version}`,
    client_type: clientInfo.browser,
    app_version: import.meta.env.VITE_APP_BASE_VERSION || _package.version,
    store_id: userStore.resellerInfo?.reseller_id,
    rtm: userStore.resellerInfo?.rtm,
    rtm_type: userStore.resellerInfo?.rtm_type,
    sub_rtm: userStore.resellerInfo?.sub_rtm,
    event_type: "expose",
    prs_id: getPrsId(),
    prstypecode: userStore.userInfo?.prsTypeCode,
  };
};

// 格式化参数
const formatParams = (params) => {
  const formData = new FormData();
  Object.keys(params).forEach((key) => {
    formData.append(key, toString(params[key]));
  });
  return formData;
};

// 将对象转换为线性对象
function ObjectKeyToLine(obj, prefix = "") {
  const res = {};
  if (!isEmptyObj(obj)) {
    for (const key in obj) {
      res[prefix + camelCaseToLine(key)] = toString(obj[key]);
    }
  }
  return res;
}

// 添加公共参数
const addLogCommonParams = (params) => {
  const userStore = getUserStore();
  console.log("🚀 ~ addLogCommonParams ~ params:", userStore.resellerInfo);
  const { currentRoute } = router;
  const { extra, extra_json, ...args } = params; // 添加 extra_json 是为了兼容老的代码，并且满足新的需求，新需求是 参数过多的情况直接 JSON.stringify 传递 extra_json 字段 key 为 extra
  return {
    ...commonParams(),
    ...ObjectKeyToLine(extra, "customized_"),
    ...ObjectKeyToLine(args),
    client_time: `${Date.now()}`,
    page_name: currentRoute.value.name,
    ...(extra_json && { extra: JSON.stringify(extra_json) }), // 兼容老的代码，并且满足新的需求
  };
};

// 图片发送
const imageSend = (params) => {
  const img = new Image();
  img.style.display = "none";
  const removeImage = function () {
    img.parentNode.removeChild(img);
  };
  img.onload = removeImage;
  img.onerror = removeImage;

  const data = new URLSearchParams(params);
  img.src = `${REPORTURL}?${data}`;
  document.body.appendChild(img);
};

// 发送日志
/**
 * @param {string}  eventName  事件名 必传
 * @param {"expose" | "click" | "submit"}  eventType  事件类型 必传
 * @param {JSONString}  extra  自定义参数 可选
 * @param {number}  value  计算属性 可选
 * @param {number}  eventTime  时间计算属性 可选
 * @param {string}  enterfrom  来源 可选
 *
 */

export const sendLog = (params) => {
  const logParams = addLogCommonParams(params);
  const userStore = getUserStore();
  const posId = userStore.resellerInfo?.reseller_id;
  const urlWithId = `${REPORTURL}?store_id=${posId}`;
  // 生产环境使用 navigator.sendBeacon 发送日志
  if (import.meta.env.VITE_APP_ENV === "production") {
    if (navigator.sendBeacon) {
      const formData = formatParams(logParams);
      const data = navigator.sendBeacon(urlWithId, formData);
      if (!data) {
        imageSend(logParams);
      }
    } else {
      imageSend(logParams);
    }
  } else {
    console.log("🚀 ~ sendLog ~ logParams:", logParams);

    let log = `[sendLog] %c${logParams.event_name}[${logParams.event_type}]`;

    if (logParams.event_time) log += `, event_time: ${logParams.event_time}`;
    if (logParams.customized_type) log += `, type: ${logParams.customized_type}`;
    if (logParams.customized_lob) log += `, lob: ${logParams.customized_lob}`;
    if (logParams.customized_id) log += `, id: ${logParams.customized_id}`;
    if (logParams.prs_type_code) log += `, prs_type_code: ${logParams.prs_type_code}`;
    if (logParams.prs_id) log += `, prs_id: ${logParams.prs_id}`;

    console.log(log, "font-size: 2em; color: #339933");
    console.log(logParams);
  }
};

// BUGFIX 至少发一次log
export function fixSendLog(params) {
  const logParams = addLogCommonParams(params);
  const userStore = getUserStore();
  const posId = userStore.resellerInfo?.reseller_id;
  const urlWithId = `${REPORTURL}?store_id=${posId}`;
  if (import.meta.env.VITE_APP_ENV === "production") {
    const formData = formatParams(logParams);
    // 同步请求
    axios.post(urlWithId, formData).then((data) => console.log(data));
  } else {
    console.log(
      `[fixSendLog] %c${logParams?.event_name}, ${logParams?.event_type}, type: ${logParams?.customized_type}`,
      "font-size: 2em; color: #339933"
    );
    console.log(logParams);
  }
}
