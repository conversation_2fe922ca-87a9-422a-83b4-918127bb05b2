/*
 * @Date: 2025-07-18 16:05:42
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-18 16:12:26
 * @FilePath: /MyBusinessV2/src/utils/sortCycle.ts
 */
export function parseCycle(str: string): [number, number, number] {
  const match = str.match(/^FY(\d+)Q(\d+)W(\d+)$/);
  if (!match) return [0, 0, 0];
  return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
}

/**
 * 对周期字符串数组排序
 * @param arr 周期字符串数组
 * @param order 'asc' 升序（默认），'desc' 降序
 */
export function sortCycles(arr: string[], order: "asc" | "desc" = "asc"): string[] {
  return arr.slice().sort((a, b) => {
    const [ay, aq, aw] = parseCycle(a);
    const [by, bq, bw] = parseCycle(b);
    if (ay !== by) return order === "asc" ? ay - by : by - ay;
    if (aq !== bq) return order === "asc" ? aq - bq : bq - aq;
    return order === "asc" ? aw - bw : bw - aw;
  });
}
