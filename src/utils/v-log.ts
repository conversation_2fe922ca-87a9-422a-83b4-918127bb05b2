import { sendLog } from "./sendLog.js";

// 配置常量
const LOG_CONFIG = {
  MIN_STAY_TIME: 2000, // 最小停留时长
  DEFAULT_STAY_ATTRIBUTE: "mybusiness_stay_report", // 默认停留埋点属性
  DEFAULT_EXPOSE_ATTRIBUTE: "mybusiness_export_report", // 默认曝光埋点属性
};

let logArea = null;
// const visuallyList = [] // 记录已经上报过的埋点信息
const stayList = {}; // 记录当前页面停留元素
// eslint-disable-next-line no-unused-vars
// const appStartTime = Date.now(); // 记录app开始时间
// eslint-disable-next-line no-unused-vars
// let domHiddenTime = 0; // 记录页面离屏总时长
// let onceDomHiddenStartTimeStamp = 0; // 记录本次离屏开始时间

// BUG fix Safari 新建tab 第一次进入就会触发document.hidden = false 旧窗口打卡则不会
let BUFNOSHOW = true;

// 刷新，关闭页面时即会触发beforeunloadReportData 又会触发hidden，防止发送两次
let pageShowStayHadSend = null;

// 优化：减少阈值数量，提高性能
const options = {
  root: null, // 默认浏览器视窗
  threshold: [0.5], // 简化为只需50%可见
};

const exposeOptions = {
  root: null, // 默认浏览器视窗
  threshold: [0.5], // 简化为只需50%可见
};

/**
 * 从元素获取埋点数据
 * @param {HTMLElement} element - DOM元素
 * @param {string} attributeName - 属性名
 * @returns {Object|null} 解析后的数据或null
 */
function getElementData(element, attributeName) {
  try {
    // 首先尝试从元素的_logData获取
    if (element._logData && element._logData[attributeName]) {
      return element._logData[attributeName];
    }

    // 然后尝试从属性获取
    const dataStr = element.getAttribute(attributeName);
    if (!dataStr) return null;

    const data = JSON.parse(dataStr);

    // 存储解析后的数据到元素
    if (!element._logData) element._logData = {};
    element._logData[attributeName] = data;

    return data;
  } catch (e) {
    console.error("埋点数据格式异常", e);
    return null;
  }
}

const callback = (entries, observer) => {
  entries.forEach((entry) => {
    // 使用优化的数据获取方法
    const visibleData = getElementData(
      entry.target,
      LOG_CONFIG.DEFAULT_EXPOSE_ATTRIBUTE,
    );

    // 没有埋点数据取消上报
    if (!visibleData) {
      observer?.unobserve(entry.target);
      return;
    }

    if (entry.isIntersecting) {
      // 上报埋点信息
      sendLog({
        ...visibleData,
        eventType: "expose",
      });
      // visuallyList?.push(visibleData.eventName)
      observer?.unobserve(entry.target);
    }
  });
};

export const reportAllStayList = (afterDel) => {
  Object.keys(stayList).forEach((key) => {
    const eventTime = Date.now() - stayList[key].startTime;
    if (eventTime > LOG_CONFIG.MIN_STAY_TIME) {
      sendLog({
        ...stayList[key].visibleData,
        eventTime,
        eventType: "stay",
      });
    }
    afterDel && delete stayList[key];
  });
};

document.addEventListener("visibilitychange", function () {
  // 离屏元素停留
  Object.keys(stayList).forEach((key) => {
    if (document.hidden) {
      if (pageShowStayHadSend !== true) {
        const eventTime = Date.now() - stayList[key].startTime;
        if (eventTime > LOG_CONFIG.MIN_STAY_TIME) {
          sendLog({
            ...stayList[key].visibleData,
            eventTime,
            eventType: "stay",
          });
        }
      }
    } else {
      stayList[key].startTime = Date.now();
    }
  });

  // 离屏页面停留
  if (document.hidden) {
    BUFNOSHOW = false;
    if (pageShowStayHadSend !== true) {
      if (logArea) {
        sendLog({
          ...logArea,
          visibilitychange: true,
          eventTime: Date.now() - logArea.eventTime,
        });
      }
      pageShowStayHadSend = true;
      // onceDomHiddenStartTimeStamp = Date.now();
    }
  } else {
    pageShowStayHadSend = false;
    if (BUFNOSHOW) return;
    if (logArea) {
      logArea.eventTime = Date.now();
    }

    // domHiddenTime += Date.now() - onceDomHiddenStartTimeStamp;
    // onceDomHiddenStartTimeStamp = 0;
  }
});

const stayCallback = (entries, observer) => {
  entries.forEach((entry) => {
    // 使用优化的数据获取方法
    const visibleData = getElementData(
      entry.target,
      LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE,
    );

    // 没有埋点数据取消上报
    if (!visibleData) {
      observer?.unobserve(entry.target);
      return;
    }

    const { event_name } = visibleData;
    if (entry.isIntersecting) {
      // 上报埋点信息
      stayList[event_name] = stayList[event_name] || {
        visibleData,
        startTime: Date.now(),
        element: entry.target, // 存储元素引用
      };
    } else {
      if (stayList[event_name]) {
        const eventTime = Date.now() - stayList[event_name].startTime;
        if (eventTime > LOG_CONFIG.MIN_STAY_TIME) {
          sendLog({
            ...visibleData,
            eventTime,
            eventType: "stay",
          });
        }

        delete stayList[event_name];
      }
    }
  });
};

const observer = new IntersectionObserver(callback, exposeOptions);
const stayObserver = new IntersectionObserver(stayCallback, options);

export const addListener = (ele, binding) => {
  if (!ele) return;

  const { modifiers, value } = binding;

  // 存储埋点数据到元素
  if (!ele._logData) ele._logData = {};

  const { click, visible, stay } = modifiers;

  // 点击埋点
  if (click) {
    // 存储点击处理函数，便于后续移除
    const clickHandler = () => {
      sendLog({
        ...value,
        eventType: "click",
      });
    };

    ele._logData.clickHandler = clickHandler;
    ele.addEventListener("click", clickHandler);
  }

  // 曝光埋点
  if (visible) {
    // if (visuallyList.indexOf(eventName) !== -1) return
    ele._logData[LOG_CONFIG.DEFAULT_EXPOSE_ATTRIBUTE] = value;
    ele.setAttribute(
      LOG_CONFIG.DEFAULT_EXPOSE_ATTRIBUTE,
      JSON.stringify(value),
    );
    observer.observe(ele);
  }

  // 停留埋点
  if (stay) {
    ele._logData[LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE] = value;
    ele.setAttribute(LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE, JSON.stringify(value));
    stayObserver.observe(ele);
  }
};

export const updatedListener = (ele, binding) => {
  if (!ele) return;

  const { value, modifiers } = binding; // 获取绑定的值
  const { event_name } = value; // 获取埋点数据
  const { visible, stay } = modifiers;

  // 更新停留列表中的数据
  if (stayList[event_name]) {
    stayList[event_name].visibleData = value; // 更新 离开页面时上报的数据
  }

  // 更新元素存储的数据
  if (!ele._logData) ele._logData = {};

  // 更新曝光埋点数据
  if (visible) {
    ele._logData[LOG_CONFIG.DEFAULT_EXPOSE_ATTRIBUTE] = value;
    ele.setAttribute(
      LOG_CONFIG.DEFAULT_EXPOSE_ATTRIBUTE,
      JSON.stringify(value),
    ); // 更新当前页面滚动时候满足曝光条件的数据
  }

  // 更新停留埋点数据
  if (stay) {
    ele._logData[LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE] = value;
    ele.setAttribute(LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE, JSON.stringify(value)); // 更新当前页面滚动时候满足停留条件的数据
  }
};

export const removeListener = (ele, binding = {}) => {
  if (!ele) return;

  const { modifiers = {} } = binding;
  const { visible = false, stay = false } = modifiers;

  // 移除点击监听
  if (ele._logData?.clickHandler) {
    ele.removeEventListener("click", ele._logData.clickHandler);
  }

  // 移除曝光观察
  if (visible) {
    observer?.unobserve(ele);
  }

  // 移除停留观察并发送最终数据
  if (stay) {
    // 获取元素数据
    const visibleData = getElementData(ele, LOG_CONFIG.DEFAULT_STAY_ATTRIBUTE);

    if (visibleData) {
      const { event_name } = visibleData;

      // 处理停留中的元素
      if (stayList[event_name]) {
        const eventTime = Date.now() - stayList[event_name].startTime;
        if (eventTime > LOG_CONFIG.MIN_STAY_TIME) {
          sendLog({
            ...visibleData,
            eventTime,
            eventType: "stay",
          });
        }

        delete stayList[event_name];
      }
    }

    stayObserver?.unobserve(ele);
  }
};

export const beforeEach = (to, from, next) => {
  if (logArea) {
    sendLog({
      ...logArea,
      eventTime: Date.now() - logArea.eventTime,
    });
  }

  let data = {
    eventName: to.name,
    eventType: "stay",
    enterFrom: from.name,
    eventTime: Date.now(),
  };

  // 在路由种如果有:id，则添加到extra中，for日报和周报的stay
  if (to.params.id) {
    data = { ...data, extra: { id: to.params.id } };
  }

  // 切换路由元素听力
  logArea = { ...data };

  // 切换路由面元素停留
  reportAllStayList(true);
  next();
};

function beforeunloadReportData() {
  if (pageShowStayHadSend) return;

  // 退出页面停留
  if (logArea && !document.hidden) {
    sendLog({
      ...logArea,
      eventTime: Date.now() - logArea.eventTime,
      beforeunload: true,
    });
  }

  // 退出面元素停留
  reportAllStayList(true);
  pageShowStayHadSend = true;
}

window.addEventListener("beforeunload", beforeunloadReportData);
