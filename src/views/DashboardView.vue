<script setup lang="ts">
import { useUserStore } from "@/stores";
import { useAppStore } from "@/stores/app.js";
import ResellerLevelIcon from "@/assets/icons/reseller-level-icon.svg";
import DashboardHealth from "@/views/components/DashboardHealth/DashboardHealth.vue";
import DashboardOperationCenter from "@/views/components/DashboardOperationCenter/DashboardOperationCenter.vue";
import DashboardMessageCenter from "@/views/components/DashboardMessageCenter/DashboardMessageCenter.vue";
// import DashboardDataCenter from "@/views/components/DashboardDataCenter/DashboardCenter.vue";
import DashboardDataCenter from "@/views/components/DashboardDataCenter/DashboardDataCenter.vue";
import { loadLanguageAsync } from "@/i18n";
import { i18n } from "@/i18n";
import DmpPopoverProvider from "@dmp/components/src/popoverProvider/popoverProvider.vue";
import { useSchemaStore } from "@/stores/schema";
import { name2CompMap } from "@/constants/schema/name2CompMap";

const userStore = useUserStore();
const appStore = useAppStore();
const schemaStore = useSchemaStore();

// 获取响应式状态
const isMobile = computed(() => appStore.isMobile);

// 语言选择
const language = ref(i18n.global.locale);

const changeLanguage = (lang) => {
  loadLanguageAsync(lang);
};

onMounted(() => {
  if (userStore.isAuthenticated) {
    notify.success("测试notify组件功能是否正常");
  }
});
</script>

<template>
  <!-- <div>
    <el-select v-model="language" @change="changeLanguage" style="width: 240px">
      <el-option label="中文" value="zh" />
      <el-option label="英文" value="en" />
      <el-option label="繁体" value="hk" />
      <el-option label="简体" value="tw" />
    </el-select>
  </div> -->
  <dmp-popover-provider
    :schema="schemaStore.schema"
    :comps-registry="name2CompMap"
    class="space-y-[24px]"
  >
    <div :class="['m-auto', isMobile ? 'w-[370px]' : 'w-auto']">
      <!-- Header -->
      <div
        :class="[
          'flex items-center gap-[16px]',
          // 移动端垂直布局，桌面端水平布局
          isMobile ? 'flex-col items-start' : 'justify-between',
        ]"
      >
        <div class="flex gap-[8px] items-center w-full">
          <h1
            class="text-[24px] text-black font-bold truncate text-ellipsis overflow-hidden flex-1"
          >
            {{ userStore.resellerInfo?.reseller_name || "-" }}
          </h1>
          <div
            class="flex items-center gap-[2px] bg-[rgba(255,255,255,.32)] rounded-[28px] px-[6px] py-[2px] shrink-0"
          >
            <ResellerLevelIcon class="w-[12px] h-[12px]" />
            <span class="text-[12px] text-[#000000]">
              {{ userStore.resellerInfo?.rtm || "-" }}
            </span>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div
        :class="[
          'flex gap-[16px] mt-[24px]',
          // 移动端垂直布局，桌面端水平布局
          isMobile ? 'flex-col' : 'flex-row',
        ]"
      >
        <!-- 健康诊断组件 -->
        <div
          :class="[
            'flex flex-col gap-[16px]',
            // 移动端全宽，桌面端固定宽度
            isMobile ? 'w-full' : 'w-[332px] shrink-0',
          ]"
        >
          <DashboardHealth
            class="w-full h-[544px]"
            v-log.visible="{ event_name: 'dashboard_health' }"
            v-log.stay="{ event_name: 'dashboard_health' }"
          />
        </div>

        <!-- 消息中心和运营中心 -->
        <div
          class="flex flex-col gap-[16px] overflow-hidden shrink-0"
          :class="[isMobile ? 'w-full' : 'w-[720px]']"
        >
          <DashboardMessageCenter
            :class="[
              'w-full',
              // 移动端调整高度，桌面端保持原高度
              isMobile ? 'h-auto min-h-[120px]' : 'h-[144px]',
            ]"
          />

          <!-- 运营中心 -->
          <DashboardOperationCenter :class="['w-full', isMobile ? 'h-auto' : 'h-[384px]']" />
        </div>
      </div>
      <!-- 数据中心 -->
      <DashboardDataCenter
        :class="[
          'mt-[16px]',
          isMobile ? 'w-full' : 'w-[1068px]',
          // 移动端调整高度，桌面端保持原高度
          isMobile ? 'h-auto min-h-[300px]' : 'h-auto max-h-[384px]',
        ]"
      />
    </div>
  </dmp-popover-provider>
</template>

<style scoped>
/* 确保卡片在小屏幕上的响应式行为 */
@media (max-width: 1024px) {
  .col-span-12 {
    grid-column: span 12;
  }
}

/* 卡片悬停效果 */
.bg-white:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.dashboard-score-wrapper {
  background: url(../assets/dashboard/dashboard-score-bg.png) rgba(255, 255, 255, 0.32);
  background-size: cover;
}
</style>
