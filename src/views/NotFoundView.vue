<script setup>
import { useRouter } from "vue-router";

const router = useRouter();

function goHome() {
  router.push("/");
}

function goBack() {
  router.go(-1);
}
</script>

<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100"
  >
    <div class="max-w-md w-full mx-auto text-center">
      <div class="mb-8">
        <!-- 404 大数字 -->
        <div
          class="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600 mb-4"
        >
          404
        </div>

        <!-- 错误信息 -->
        <h1 class="text-2xl font-semibold text-gray-800 mb-4">页面未找到</h1>

        <p class="text-gray-600 mb-8">抱歉，您访问的页面不存在或已被移动。</p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          @click="goHome"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
        >
          返回首页
        </button>

        <button
          @click="goBack"
          class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200 font-medium"
        >
          返回上页
        </button>
      </div>
    </div>
  </div>
</template>
