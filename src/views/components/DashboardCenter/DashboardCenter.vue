<script setup lang="ts">
import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { name2CompMap } from "@/constants/schema/name2CompMap.ts";
import { useAppStore } from "@/stores/app.js";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useSchemaStore } from "@/stores/schema.ts";
import { DashboardCenterProps } from "@/views/components/DashboardCenter/props.ts";

const { id, title, updateTime, gridTemplates, gridSizes } = defineProps<DashboardCenterProps>();

const appStore = useAppStore();
const schemaStore = useSchemaStore();
const { t } = useI18n();
const isMobile = computed(() => appStore.isMobile);

const gridTemplate = computed(() =>
  isMobile.value ? gridTemplates.mobile : gridTemplates.desktop
);
const gridSize = computed(() => (isMobile.value ? gridSizes.mobile : gridSizes.desktop));
const contentSize = computed(() =>
  isMobile.value
    ? { width: "100%", height: "auto" }
    : {
        width: "100%",
        height: "auto",
      }
);
const dataList = ref([]);
const loading = ref(true);

const layoutComps = computed(
  () => schemaStore.schema.layout.desktop.find((i) => i.id === id)?.children || []
);
const intermediatesManager = IntermediatesManager.instance;

onMounted(async () => {
  dataList.value = await Promise.all(
    layoutComps.value.map((comp) => intermediatesManager.get(comp.type, comp.api)?.())
  );
  loading.value = false;
});

const layoutCompsReady = computed(() =>
  layoutComps.value.reduce((prev, comp, index) => {
    if (dataList.value[index]) prev.push({ ...comp, data: dataList.value[index] });
    return prev;
  }, [])
);
</script>

<template>
  <dmp-glass-card
    :title="t(title)"
    :loading
    :grid-template
    :grid-size
    :content-size
    :disable-scrolling="isMobile"
  >
    <component
      v-for="comp in layoutCompsReady"
      :key="comp.title + comp.type"
      :id="comp.id"
      :title="comp.title"
      :is="name2CompMap[comp.type]"
      v-bind="comp.data"
      :update-time="updateTime"
      :isMobile="isMobile"
    />
  </dmp-glass-card>
</template>

<style lang="scss" scoped>
:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }
}
</style>
