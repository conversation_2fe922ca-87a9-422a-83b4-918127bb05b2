<script setup>
import DashboardCenter from "@/views/components/DashboardCenter/DashboardCenter.vue";

const gridTemplates = {
  desktop: {
    col: 1,
    row: 1,
  },
  mobile: {
    col: 1,
    row: 1,
  },
};

const gridSizes = {
  desktop: {
    row: "472",
    col: "300",
  },
  mobile: {
    row: "472",
    col: "338",
  },
};
</script>

<template>
  <DashboardCenter
    id="healthDiagnosis"
    title="健康诊断"
    :updateTime="'更新时间 TODO'"
    :grid-templates
    :grid-sizes
    class="w-full"
  />
</template>
