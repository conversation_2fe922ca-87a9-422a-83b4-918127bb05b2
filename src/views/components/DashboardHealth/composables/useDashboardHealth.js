export const useDashboardHealth = () => {
  const colorRange = (score) => {
    const colorMap = {
      "0-59": ["#F63F54", "#FF8175"],
      "60-79": ["#FF9500", "#FFE167"],
      "80-100": ["#2EBB72", "#9DE76A"],
    };

    for (const [range, colors] of Object.entries(colorMap)) {
      const [min, max] = range.split("-").map(Number);
      if (score >= min && score <= max) {
        return colors;
      }
    }
  };
  const items = ref([
    {
      label: "库存预警",
      progress: 100,
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      showText: true,
      progressUnit: "次",
    },
    {
      label: "7天激活率",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FFE167", "#FF9500"],
      title: "库存预警",
      progress: 66,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "同城激活率",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 77,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "销售预警",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 88,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "扫码预警",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 99,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "门店缺货",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 100,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "在仓库龄",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 100,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "库存提醒",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "normal",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 100,
      showText: true,
      progressUnit: "次",
    },
    {
      label: "库存提醒",
      color: "rgba(28, 28, 28, 0.24)",
      statusText: "暂无异常",
      statusLevel: "warning",
      gradient: ["#FF8175", "#F63F54"],
      title: "库存预警",
      progress: 100,
      showText: true,
      progressUnit: "次",
    },
  ]);

  // 计算圆点位置
  function updateDotPositions() {
    nextTick(() => {
      const progressBars = document.querySelectorAll(".progress-bar");
      progressBars.forEach((progressBar) => {
        const progressOuter = progressBar.querySelector(".el-progress-bar__outer");
        const progressInner = progressBar.querySelector(".el-progress-bar__inner");
        const dot = progressBar.querySelector(".custom-dot");

        if (progressOuter && progressInner && dot) {
          // 获取外层容器的宽度
          const outerWidth = progressOuter.offsetWidth;
          // 从 data-percentage 属性获取实际的进度百分比
          const percentage = parseInt(progressBar.dataset.percentage) || 0;
          // 计算圆点位置
          const dotPosition = (outerWidth * percentage) / 100;
          dot.style.left = `${dotPosition}px`;
        }
      });
    });
  }

  return {
    items,
    updateDotPositions,
    colorRange,
  };
};
