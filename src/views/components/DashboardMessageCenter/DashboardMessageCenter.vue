<script setup>
import DashboardHealthTitleIcon from "./assets/dashboard-health-title-icon.svg";
</script>

<template>
  <GlassCard title="消息中心" :icon="DashboardHealthTitleIcon">
    <div class="message-grid-container">
      <ComingSoon>
        <div class="text-[12px] font-medium text-[rgba(28,28,28,.64)] leading-[32px]">
          模块建设中...
        </div>
      </ComingSoon>
    </div>
  </GlassCard>
</template>

<style scoped>
.message-grid-container {
  overflow-x: auto;
  overflow-y: hidden;
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  border-radius: 16px;
}

.message-grid-container::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.desktop-grid {
  display: grid;
  grid-template-rows: repeat(1, 1fr);
  grid-auto-flow: column;
  gap: 8px;
  min-width: fit-content;
  height: 72px;
}

.mobile-grid {
  display: grid;
  grid-template-rows: repeat(1, 1fr);
  grid-auto-flow: column;
  gap: 6px;
  min-width: fit-content;
  height: 60px;
  -webkit-overflow-scrolling: touch;
}

/* 移动端优化触摸体验 */
@media (max-width: 768px) {
  .message-grid-container {
    scroll-behavior: smooth;
  }
}
</style>
