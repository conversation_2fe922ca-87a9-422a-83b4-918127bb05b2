<script setup>
import DashboardCenter from "@/views/components/DashboardCenter/DashboardCenter.vue";

const gridTemplates = {
  desktop: {
    col: 2,
  },
  mobile: {
    col: 1,
  },
};

const gridSizes = {
  desktop: {
    row: 312,
    col: 340,
  },
  mobile: {
    row: 312,
    col: 338,
  },
};
</script>

<template>
  <DashboardCenter
    id="operationCenter"
    title="运营中心"
    :updateTime="'更新时间 TODO'"
    :grid-templates
    :grid-sizes
    class="w-full"
  />
</template>
