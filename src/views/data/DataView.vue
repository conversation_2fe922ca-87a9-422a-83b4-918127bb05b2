<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          {{ pageTitle }}
        </h1>
        <p class="text-gray-600">
          {{ pageDescription }}
        </p>
      </div>

      <!-- 内容区域 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="text-center py-12">
          <div
            class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4"
          >
            <svg
              class="h-6 w-6 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            {{ pageTitle }}
          </h3>
          <p class="text-gray-500 mb-4">该页面正在开发中，敬请期待...</p>
          <div class="flex justify-center space-x-4">
            <button
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              开始使用
            </button>
            <button
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              了解更多
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

// 根据路由动态设置页面标题和描述
const pageTitle = computed(() => {
  return route.meta?.title || "数据中心";
});

const pageDescription = computed(() => {
  const descriptions = {
    销售表现: "查看销售数据分析、趋势图表和关键指标",
    库存表现: "监控库存水平、周转率和预警信息",
    零售项目: "管理零售项目数据和绩效指标",
    配件排行: "查看配件销售排行和热门产品分析",
    门店客流: "监控门店客流量和客户行为数据",
    "SEED 平均等级": "查看SEED等级分布和平均水平统计",
    "DCOTA 点亮率": "监控DCOTA系统的点亮率和使用情况",
    运营异常: "查看运营过程中的异常情况和处理记录",
    我的申报: "管理个人申报信息和审批状态",
    备货计划: "制定和管理备货计划，优化库存配置",
    分货计划: "安排分货计划和配送管理",
    返点表现: "查看返点政策执行情况和效果分析",
    运营评分: "查看运营绩效评分和改进建议",
    活动管理: "管理营销活动和促销方案",
    提升项目: "跟踪业务提升项目的进展和效果",
  };

  return descriptions[pageTitle.value] || "数据管理和分析平台";
});
</script>
