<script setup>
import { ref } from "vue";

const pageTitle = ref("运营数据");
const subtitle = ref("运营中心 - 数据统计分析");
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
        <p class="text-gray-600 mt-1">{{ subtitle }}</p>
      </div>
    </div>

    <!-- 运营数据内容 -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">运营数据统计</h3>
      <div class="text-center py-12 text-gray-500">
        <span class="text-4xl mb-4 block">⚙️</span>
        <p>运营数据管理</p>
        <p class="text-sm mt-2">包含运营效率、绩效指标、流程优化等数据</p>
      </div>
    </div>
  </div>
</template>
