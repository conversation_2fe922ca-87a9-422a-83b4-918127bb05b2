<script setup lang="ts">
import { onMounted, ref, watch, watchEffect, Ref } from "vue";
import { getScoreDetailsDialogInfo } from "@/api/index.js";
import { sortCycles } from "@/utils/sortCycle.ts";

// 类型定义
interface Metric {
  cn_name: string;
  total_score: number;
  score: number;
}
interface ScoreDetail {
  fiscal_qtr_week_name: string;
  metrics: Metric[];
  total_week_trend?: any[];
  so_warning_week_trend?: any[];
  inv_warning_week_trend?: any[];
  inv_remind_week_trend?: any[];
}
interface Tab {
  label: string;
  value: string;
}
interface Column {
  label: string;
  prop: string;
}

const cycles = ref<string[]>([]);
const currentCycle = ref<string | null>(null);
const healthData = ref<Metric[]>([]);
const xAxisData = ref<string[]>([]);
const series = ref<{ data: number[] }[]>([]);
const state = ref<ScoreDetail[]>([]);
const columns = ref<Column[]>([
  { label: "指標", prop: "cn_name" },
  { label: "滿分", prop: "total_score" },
  { label: "得分", prop: "score" },
]);
const tabs = ref<Tab[]>([
  { label: "整体", value: "total_week_trend" },
  { label: "销售预警", value: "so_warning_week_trend" },
  { label: "库存预警", value: "inv_warning_week_trend" },
  { label: "库存提醒", value: "inv_remind_week_trend" },
]);
const unitMap: Record<string, string> = {
  total_score: "分",
  score: "分",
};

const getBaseInfo = async () => {
  const res = await getScoreDetailsDialogInfo();
  state.value = res.data;
  cycles.value = sortCycles(res.data.map((item: ScoreDetail) => item.fiscal_qtr_week_name));
  currentCycle.value = cycles.value[0] ?? null;
  handleChange("total_week_trend");
};
watch(
  currentCycle,
  (val) => {
    const found = state.value.find((item) => item.fiscal_qtr_week_name === val);
    if (found) {
      healthData.value = found.metrics;
    } else {
      healthData.value = [];
    }
  },
  {
    immediate: true,
  }
);
// 后期替换 v-model
const handleChange = (val: string) => {
  const found = state.value.find((item) => item.fiscal_qtr_week_name === currentCycle.value);
  if (found) {
    const echartData = found[val] as any[];
    xAxisData.value = echartData.map((item) => item.week_name);
    series.value = [{ data: echartData.map((item) => item.score) }];
  }
};
watchEffect(async () => {
  getBaseInfo();
});
</script>

<template>
  <div class="health-diagnosis-dialog">
    <dmp-cycle-selector v-model="currentCycle" :cycles="cycles" class="mt-1" />
    <dmp-detail-table
      :columns="columns"
      :data="healthData"
      class="mb-4"
      title="得分詳情"
      :unitMap="unitMap"
      :highlightRule="(row, col) => col.prop === 'score' && row.score < row.total_score"
    />
    <div class="flex flex-col">
      <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E] mb-2">健康度趨勢</h1>
      <dmp-tab-switcher :tabs="tabs" :equal-width="true" @change="handleChange" />
      <dmp-line-chart
        title=""
        subtitle="每週健康度分數"
        :xAxisData="xAxisData"
        :series="series"
        :yMin="0"
        :yInterval="200"
        :yMax="100"
      />
    </div>
  </div>
</template>
