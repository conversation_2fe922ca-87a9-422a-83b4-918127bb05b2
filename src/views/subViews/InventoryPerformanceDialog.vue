<!--
 * @Date: 2025-07-16 18:44:17
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-18 17:55:33
 * @FilePath: /MyBusinessV2/src/views/subViews/InventoryPerformanceDialog.vue
-->
<template>
  <!-- 库存表现 -->
  <div class="sales-performance-dialog">
    <dmp-cycle-selector v-model="currentCycle" :cycles="cycles" />
    <dmp-detail-table
      title="庫存詳情"
      :columns="columns"
      :data="tableData"
      class="mb-4 mt-1"
      :unitMap="unitMap"
    />
    <dmp-bar-stack-chart
      title="庫存分佈"
      subtitle="每日子产品"
      :xAxisData="['W10', 'W12', 'W1', 'W3', 'W5', 'W7', 'W9']"
      :series="[
        { name: '在倉', data: [20, 10, 12, 18, 15, 13, 11] },
        { name: '严重', data: [18, 12, 14, 16, 13, 12, 10] },
      ]"
      :legendNames="['在倉', '門店']"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { getInventoryDialogInfo, getInventoryEchartDialogInfo } from "@/api/index.js";
import { sortCycles } from "@/utils/sortCycle.ts";

const props = defineProps<{ lob?: string }>();
const lob = props.lob ?? "iPhone";

// 类型定义
interface Metric {
  name: string;
  value: number | string;
  value_type: string;
  unit?: string;
}
interface InventoryDetail {
  fiscal_qtr_week_name: string;
  inv_data: Record<string, any>[];
}
interface Column {
  label: string;
  prop: string;
}

const cycles = ref<string[]>([]);
const currentCycle = ref<string | null>(null);
const tableData = ref<Record<string, any>[]>([]);
const columns = ref<Column[]>([
  { label: "", prop: "sub_lob" },
  { label: "庫存数量", prop: "inv" },
  { label: "庫存周转", prop: "woi" },
]);
const xAxisData = ref<string[]>([]); // x轴数据
const series = ref<{ data: number[] }[]>([]); // y轴数据
const state = ref<InventoryDetail[]>([]);
const unitMap: Record<string, string> = {
  inv: "台",
  woi: "周",
};
const getEchartData = () => {
  getInventoryEchartDialogInfo({ lob }).then((res) => {
    xAxisData.value = res.data.map((item: any) => item.fiscal_week);
    series.value.push({
      data: res.data.map((item: any) => item.so),
    });
  });
};
const getBaseInfo = async () => {
  const res = await getInventoryDialogInfo({ lob });
  state.value = res.data;
  cycles.value = sortCycles(res.data.map((item: InventoryDetail) => item.fiscal_qtr_week_name));
  currentCycle.value = cycles.value[0] ?? null;
};
watch(
  currentCycle,
  (val) => {
    if (!val) return;
    const found = state.value.find((item) => item.fiscal_qtr_week_name === val);
    if (found) {
      tableData.value = found.inv_data;
    } else {
      tableData.value = [];
    }
  },
  { immediate: true }
);
onMounted(async () => {
  getBaseInfo();
  // todo
  // getEchartData();
});
</script>
