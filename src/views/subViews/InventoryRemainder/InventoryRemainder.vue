<script setup lang="ts">
import { useInventoryRemainder } from "./hooks/useInventoryRemainder";

const { currentCycle, cycles, tabs, initData, dates, tableData, currentLob, columns } =
  useInventoryRemainder();

onMounted(() => {
  initData();
});

const handleTabChange = (val: string) => {
  currentLob.value = val;
};
</script>
<template>
  <div class="inventory-remainder">
    <dmp-text-list :items="['*基於過往 5 周平均銷售得出的結果']" />
    <div class="flex flex-col">
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="dates"
        class="mb-1"
        @update:modelValue="(val) => $emit('update:currentCycle', val)"
      />
    </div>

    <div class="flex flex-col gap-[16px]" :key="currentCycle">
      <dmp-tab-switcher @change="handleTabChange" :tabs="tabs" :equal-width="true" />

      <div class="flex flex-col gap-[12px]">
        <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">库存预警</h1>

        <dmp-detail-table :columns="columns" :data="tableData" class="mb-4 mt-1" />
      </div>
    </div>
  </div>
</template>
