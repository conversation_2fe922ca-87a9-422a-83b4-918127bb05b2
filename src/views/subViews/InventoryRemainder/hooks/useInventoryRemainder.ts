import { getInventoryWarningInfo } from "@/api";
import dayjs from "dayjs";
import { ref, computed, watch } from "vue";

export function useInventoryRemainder() {
  const currentCycle = ref(null);
  const cycles = ref<string[]>([]);
  const dates = ref<string[]>([]);

  const resData = ref(new Map());
  // 添加格式化日期到原始日期的映射
  const dateMapping = ref(new Map<string, string>());

  const currentLob = ref<string>("");

  // 根据选中的日期获取对应的数据
  const currentDateData = computed(() => {
    if (!dates.value.length || !currentCycle.value) {
      return null;
    }

    // 通过格式化日期找到原始日期
    const originalDate = dateMapping.value.get(currentCycle.value);

    if (!originalDate) {
      return null;
    }

    const data = resData.value.get(originalDate);

    return data;
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    if (!currentDateData?.value?.warning_lobs?.length) {
      return [];
    }

    // 从warning_lobs中提取LOB并去重
    const uniqueLobs = [...new Set(currentDateData.value.warning_lobs.map((item) => item.lob))].map(
      (item) => ({
        label: item,
        value: item,
      })
    );

    console.log("🔍 tabs计算 - 提取的uniqueLobs:", uniqueLobs);

    return uniqueLobs;
  });

  // 工具函数：转换level名称为中文显示
  const getLevelDisplayName = (level: string) => {
    const levelMap = {
      very_severe: "非常严重",
      severe: "严重",
      medium: "中等",
    };
    return levelMap[level] || level;
  };

  // 根据选中的LOB获取对应的levels数据
  const tableData = computed(() => {
    if (!currentDateData.value?.warning_lobs?.length || !currentLob.value) return [];

    const selectedLobData = currentDateData.value.warning_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData?.levels) return [];

    return selectedLobData.levels.map((level) => ({
      level: getLevelDisplayName(level.level),
      warning_times: level.warn_cnt?.toString() || "0",
      warning_store: level.pos_cnt?.toString() || "0",
    }));
  });

  // 表格列定义
  const columns = ref([
    {
      label: "",
      prop: "level",
    },
    {
      label: "预警次数",
      prop: "warning_times",
    },
    {
      label: "预警门店",
      prop: "warning_store",
    },
  ]);

  const loadInventoryWarningInfo = async () => {
    try {
      const res = await getInventoryWarningInfo();

      if (res.data && Array.isArray(res.data)) {
        // 清空之前的映射
        dateMapping.value.clear();

        // 获取日期列表并格式化
        dates.value = res.data
          .map((item: any) => {
            if (!item?.date) return null;
            const formattedDate = dayjs(item.date).format("MM/DD");
            dateMapping.value.set(formattedDate, item.date); // 存储格式化日期到原始日期的映射
            return formattedDate;
          })
          .filter(Boolean); // 过滤掉无效日期

        // 构建数据映射
        resData.value = new Map(
          res.data
            .filter((item: any) => item?.date) // 过滤掉无效数据
            .map((item: any) => [item.date, item])
        );

        // 默认选中第一个日期
        if (dates.value.length > 0) {
          currentCycle.value = dates.value[0];
        }
      } else {
        console.log("🔍 数据为空");
      }
    } catch (error) {
      // 容错处理：重置数据
      dates.value = [];
      resData.value = new Map();
      dateMapping.value.clear();
      currentCycle.value = null;
      currentLob.value = "";
    }
  };

  // 监听日期变化，设置第一个可用的LOB
  watch(
    [currentCycle, tabs],
    () => {
      // 当日期变化或tabs变化时，自动选择第一个可用的LOB
      if (tabs.value.length > 0 && !currentLob.value) {
        currentLob.value = tabs.value[0].value as string;
      } else if (tabs.value.length === 0) {
        currentLob.value = "";
      }
    },
    { immediate: false }
  );

  const initData = () => {
    loadInventoryWarningInfo();
  };

  return {
    currentCycle,
    cycles,
    tabs,
    initData,
    dates,
    tableData,
    currentLob,
    columns,
  };
}
