<script setup lang="ts">
import { useInventoryWarningDialog } from "./hooks/useInventoryWarningDialog";
import { formatNumber } from "../../../../packages/common/utils/number";

const { currentCycle, tabs, initData, dates, currentLob, currentLobStats } =
  useInventoryWarningDialog();

onMounted(() => {
  initData();
});

const handleTabChange = (val: string) => {
  currentLob.value = val;
};
</script>
<template>
  <div class="inventory-remainder">
    <dmp-text-list :items="['*基於過往 5 周平均銷售得出的結果']" />
    <div class="flex flex-col">
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="dates"
        class="mb-1"
        @update:modelValue="(val) => $emit('update:currentCycle', val)"
      />
    </div>

    <div class="flex flex-col gap-[16px]" :key="currentCycle">
      <dmp-tab-switcher @change="handleTabChange" :tabs="tabs" :equal-width="true" />

      <div class="flex flex-col gap-[12px]">
        <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">缺货分布</h1>

        <div class="flex flex-col gap-[4px]">
          <div class="bg-[rgba(28,28,30,0.06)] rounded-[12px] p-4">
            <div class="flex justify-between h-[48px] items-center">
              <div>
                <div class="text-[12px] font-normal leading-[16px] text-[#1C1C1E]">大于5周</div>
                <div class="text-[11px] font-normal leading-[16px] text-[#AEAEB2]">
                  库存{{ formatNumber(currentLobStats.woi_greater_5.inventory) }}
                </div>
              </div>
              <dmp-value-with-unit
                :value="currentLobStats.woi_greater_5.ratio.toFixed(1)"
                unit="%"
              />
            </div>
            <div class="h-[1px] bg-[rgba(28,28,30,0.08)] my-2"></div>
            <div class="flex justify-between h-[48px] items-center">
              <div>
                <div class="text-[12px] font-normal leading-[16px] text-[#1C1C1E]">小于1周</div>
                <div class="text-[11px] font-normal leading-[16px] text-[#AEAEB2]">
                  库存{{ formatNumber(currentLobStats.woi_less_1.inventory) }}
                </div>
              </div>
              <dmp-value-with-unit :value="currentLobStats.woi_less_1.ratio.toFixed(1)" unit="%" />
            </div>
          </div>

          <div class="bg-[rgba(28,28,30,0.06)] rounded-[12px] p-4">
            <div class="flex justify-between h-[48px] items-center">
              <div>
                <div class="text-[12px] font-normal leading-[16px] text-[#1C1C1E]">累计库存</div>
              </div>
              <dmp-value-with-unit
                :value="formatNumber(currentLobStats.total_inventory)"
                unit="台"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
