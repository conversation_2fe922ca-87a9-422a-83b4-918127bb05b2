import { getInventoryRemainderInfo } from "@/api";
import dayjs from "dayjs";
import { ref, computed, watch } from "vue";

export function useInventoryWarningDialog() {
  const currentCycle = ref(null);
  const cycles = ref<string[]>([]);
  const dates = ref<string[]>([]);

  const resData = ref(new Map());
  // 添加格式化日期到原始日期的映射
  const dateMapping = ref(new Map<string, string>());

  const currentLob = ref<string>("");

  // 根据选中的日期获取对应的数据
  const currentDateData = computed(() => {
    if (!dates.value.length || !currentCycle.value) return null;

    // 通过格式化日期找到原始日期
    const originalDate = dateMapping.value.get(currentCycle.value);
    if (!originalDate) return null;

    return resData.value.get(originalDate);
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    if (!currentDateData?.value?.inv_remind_lobs?.length) return [];

    // 从inv_remind_lobs中提取LOB并去重
    const uniqueLobs = [
      ...new Set(currentDateData.value.inv_remind_lobs.map((item) => item.lob)),
    ].map((item) => ({
      label: item,
      value: item,
    }));

    // 自动选中第一个tab
    if (uniqueLobs.length > 0 && !currentLob.value) {
      currentLob.value = uniqueLobs[0].value as string;
    }

    return uniqueLobs;
  });

  // 计算当前选中LOB的库存统计数据
  const currentLobStats = computed(() => {
    if (!currentDateData.value?.inv_remind_lobs?.length || !currentLob.value) {
      return {
        woi_less_1: { inventory: 0, ratio: 0 },
        woi_greater_5: { inventory: 0, ratio: 0 },
        total_inventory: 0,
      };
    }

    const selectedLobData = currentDateData.value.inv_remind_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData) {
      return {
        woi_less_1: { inventory: 0, ratio: 0 },
        woi_greater_5: { inventory: 0, ratio: 0 },
        total_inventory: 0,
      };
    }

    const woi_less_1_inv = selectedLobData.woi_less_1_inv || 0;
    const woi_greater_5_inv = selectedLobData.woi_greater_5_inv || 0;
    const total_inventory = woi_less_1_inv + woi_greater_5_inv;

    return {
      woi_less_1: {
        inventory: woi_less_1_inv,
        ratio: (selectedLobData.woi_less_1_inv_ratio || 0) * 100,
      },
      woi_greater_5: {
        inventory: woi_greater_5_inv,
        ratio: (selectedLobData.woi_greater_5_inv_ratio || 0) * 100,
      },
      total_inventory,
    };
  });

  // 监听日期变化，重置LOB选择
  watch(currentCycle, () => {
    // 当日期变化时，重置LOB为第一个可用的LOB
    currentLob.value = "";
  });

  const loadInventoryRemainderInfo = async () => {
    try {
      const res = await getInventoryRemainderInfo();

      if (res.data && Array.isArray(res.data)) {
        // 清空之前的映射
        dateMapping.value.clear();

        // 获取日期列表并格式化
        dates.value = res.data
          .map((item: any) => {
            if (!item?.date) return null;
            const formattedDate = dayjs(item.date).format("MM/DD");
            dateMapping.value.set(formattedDate, item.date); // 存储格式化日期到原始日期的映射
            return formattedDate;
          })
          .filter(Boolean); // 过滤掉无效日期

        // 构建数据映射
        resData.value = new Map(
          res.data
            .filter((item: any) => item?.date) // 过滤掉无效数据
            .map((item: any) => [item.date, item])
        );

        // 默认选中第一个日期
        if (dates.value.length > 0) {
          currentCycle.value = dates.value[0];
        }
      }
    } catch (error) {
      console.error("加载库存提醒信息失败:", error);
      // 容错处理：重置数据
      dates.value = [];
      resData.value = new Map();
      dateMapping.value.clear();
      currentCycle.value = null;
      currentLob.value = "";
    }
  };

  const initData = () => {
    loadInventoryRemainderInfo();
  };

  return {
    currentCycle,
    cycles,
    tabs,
    initData,
    dates,
    currentLob,
    currentLobStats,
  };
}
