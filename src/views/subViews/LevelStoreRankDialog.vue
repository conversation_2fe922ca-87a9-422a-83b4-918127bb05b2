<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo } from "@/api/index";
import { formatProductRank } from "@/intermediates/format/formatProductRankDialog";
import { PopoverRankItem } from "@dmp/components/src";
import { watchEffect } from "vue";

const { lob } = defineProps<{ lob: string }>();
const { t } = useI18n();

// tab
const tabOptions = ref([
  { label: "Level A", value: "level_a" },
  { label: "Level B", value: "level_b" },
  { label: "Level C", value: "level_c" },
  { label: "Level D", value: "level_d" },
  { label: "Level E", value: "level_e" },
]);
const tabValue = ref(tabOptions.value[0].value);

// api
const rankData: Ref<Record<string, PopoverRankItem[]>> = ref({});
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ lob, start: 0, offset: 10 })
    .then(({ data = {} }) => {
      rankData.value = formatProductRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});

// computed
const currentRankData = computed(() => {
  const key = `last_week_${tabValue.value}`;
  return rankData.value?.[key] ?? [];
});
</script>

<template>
  <DmpTabSwitcher class="mt-[12px]" :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
  <DmpPopoverRank
    class="mt-[12px]"
    :name-title="t('排名')"
    :value-title="t('分数')"
    columns="['1fr', '48px']"
    :list="currentRankData"
    :loading="loading"
  >
    <template v-slot:value="{ row }">
      <DmpValueWithUnit
        :value="row?.value"
        unit="分"
        :value-class="['!text-[16px]', '!text-[#3948B1]', '!font-semibold']"
        :unit-class="['!text-[12px]', '!text-[#3948B1]', '!font-semibold']"
      />
    </template>
  </DmpPopoverRank>
</template>
