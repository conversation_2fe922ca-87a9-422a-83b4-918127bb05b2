<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo } from "@/api/index";
import { formatProductRank } from "@/intermediates/format/formatProductRankDialog";
import { PopoverRankItem } from "@dmp/components/src";
import UpSvg from "@/assets/icons/rank/up.svg";
import DownSvg from "@/assets/icons/rank/down.svg";
import { watchEffect } from "vue";

const { lob } = defineProps<{ lob: string }>();
const { t } = useI18n();

// api
const rankData: Ref<Record<string, PopoverRankItem[]>> = ref({});
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ lob, start: 0, offset: 10 })
    .then(({ data = {} }) => {
      rankData.value = formatProductRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>

<template>
  <DmpPopoverRank
    class="mt-[12px]"
    :name-title="t('排名')"
    :value-title="t('较上周排名')"
    columns="['1fr', '70px']"
    :list="rankData"
    :loading="loading"
  >
    <template v-slot:value="{ row }">
      <div
        class="flex items-center justify-start gap-[2px] text-[16px] leading-[16px] text-[#1C1C1E] font-semibold"
      >
        <UpSvg v-if="row?.isUp" />
        <DownSvg v-else />
        {{ row?.value }}
      </div>
    </template>
  </DmpPopoverRank>
</template>

<style lang="scss" scoped></style>
