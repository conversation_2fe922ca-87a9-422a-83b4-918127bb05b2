<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getProductRankDialogInfo } from "@/api/index";
import { formatProductRank } from "@/intermediates/format/formatProductRankDialog";
import { PopoverRankItem } from "@dmp/components/src";
import { watchEffect } from "vue";

const { lob } = defineProps<{ lob: string }>();
const { t } = useI18n();

// tab
const tabOptions = ref([
  { label: t("市场"), value: "market" },
  { label: t("渠道"), value: "channel" },
  { label: t("经销商"), value: "reseller" },
]);
const tabValue = ref(tabOptions.value[0].value);

// selector
const selectOptions = ref([
  { label: t("上周"), value: "last_week" },
  { label: t("季度累计"), value: "current_quarter" },
]);
const selectValue = ref(selectOptions.value[0].value);

// api
const rankData: Ref<Record<string, PopoverRankItem[]>> = ref({});
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getProductRankDialogInfo({ lob, start: 0, offset: 10 })
    .then(({ data = {} }) => {
      rankData.value = formatProductRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});

// computed
const currentRankData = computed(() => {
  const key = `${selectValue.value}_${tabValue.value}`;
  return rankData.value?.[key] ?? [];
});

// methods
const setValue = (value: string | number) => {
  return value ? `${value}%` : "-";
};
</script>

<template>
  <DmpTabSwitcher class="mt-[12px]" :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
  <DmpPopoverRank
    class="mt-[12px]"
    :value-title="t('佔有率')"
    :columns="['1fr', '60px']"
    :list="currentRankData"
    :loading="loading"
  >
    <template v-slot:nameTitle>
      <DmpSimpleDropdown v-model="selectValue" :options="selectOptions" />
    </template>
    <template v-slot:value="{ row }">
      <span class="text-[16px] leading-[20px] text-[#3948B1] font-semibold">{{
        setValue(row?.value)
      }}</span>
    </template>
  </DmpPopoverRank>
</template>
