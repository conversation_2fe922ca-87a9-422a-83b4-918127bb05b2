<template>
  <!-- 销售表现-->
  <div class="sales-performance-dialog">
    <dmp-cycle-selector v-model="currentCycle" :cycles="cycles" />
    <dmp-info-detail-table :items="salesDetails" title="銷售詳情" class="mb-4 mt-1" />
    <dmp-line-chart
      title="銷售趨勢"
      subtitle="每周销量汇总"
      :xAxisData="xAxisData"
      :series="series"
      :yMin="0"
      :yInterval="200"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, watchEffect } from "vue";
import { getSalesPerformanceDialogInfo, getSalesEchartDialogInfo } from "@/api/index.js";
import { sortCycles } from "@/utils/sortCycle.ts";
interface Metric {
  name: string;
  value: number | string;
  value_type: string;
  unit?: string;
}
interface SalesDetail {
  fiscal_qtr_week_name: string;
  metrics: Metric[];
}
const { lob } = defineProps<{ lob?: string }>();
const cycles = ref<string[]>([]);
const currentCycle = ref<string | null>(null);
const salesDetails = ref<Metric[]>([]);
const xAxisData = ref<string[]>([]); // x轴数据
const series = ref<{ data: number[] }[]>([]); // y轴数据
const state = ref<SalesDetail[]>([]);
const getEchartData = () => {
  getSalesEchartDialogInfo({ lob }).then((res) => {
    xAxisData.value = res.data.map((item) => item.fiscal_week);
    series.value.push({
      data: res.data.map((item) => item.so),
    });
  });
};
const getBaseInfo = async () => {
  const res = await getSalesPerformanceDialogInfo({ lob });
  state.value = res.data;
  cycles.value = sortCycles(res.data.map((item) => item.fiscal_qtr_week_name));
  currentCycle.value = cycles.value[0];
};
watch(currentCycle, (val) => {
  const found = state.value.find((item) => item.fiscal_qtr_week_name === val);
  if (found) {
    salesDetails.value = found.metrics;
    salesDetails.value.map((item) => {
      if (item.value_type === "number") {
        item.unit = "台";
      } else if (item.value != null && typeof item.value === "number") {
        item.value = item.value + "%";
      }
    });
  } else {
    salesDetails.value = [];
  }
});
watchEffect(async () => {
  if (lob) {
    getBaseInfo();
    getEchartData();
  }
});
</script>
