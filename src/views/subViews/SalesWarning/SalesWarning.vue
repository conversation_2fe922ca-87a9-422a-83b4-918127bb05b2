<script setup lang="ts">
import { onMounted } from "vue";
import { useSalesWarning } from "./hooks/useSalesWarning";

// 定义 emits
const emit = defineEmits(["update:currentCycle"]);

const {
  dates,
  currentCycle,
  chartsXAxisData,
  chartsSeries,

  tabs,
  tableData,
  columns,

  handleTabChange,
  initData,
} = useSalesWarning();

onMounted(() => {
  initData();
});
</script>

<template>
  <div class="inventory-remainder">
    <div class="flex flex-col gap-[12px]">
      <div class="flex flex-col gap-[4px]">
        <DmpTextList
          :items="[
            '*Lorem ipsum dolor sit amet, accusam convenire',
            '*Lorem ipsum dolor sit amet, accusam convenire quaerendum cu mei',
          ]"
        />
      </div>
      <div class="flex flex-col">
        <dmp-cycle-selector
          v-model="currentCycle"
          :cycles="dates"
          class="mb-1"
          :key="currentCycle"
          @update:modelValue="(val) => emit('update:currentCycle', val)"
        />
      </div>
    </div>

    <div class="flex flex-col gap-[16px]" :key="currentCycle">
      <dmp-tab-switcher :tabs="tabs" :equal-width="true" @change="handleTabChange" />

      <div class="flex flex-col gap-[12px]">
        <div>
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">预警分布</h1>

          <dmp-detail-table :columns="columns" :data="tableData" class="mb-4 mt-1" />
        </div>

        <div>
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">预警趋势</h1>

          <dmp-bar-stack-chart
            subtitle="每周预警次数"
            :xAxisData="chartsXAxisData"
            :series="chartsSeries"
            :legendNames="['非常严重', '严重', '中等']"
          />
        </div>
      </div>
    </div>
  </div>
</template>
