import { getSalesAlertDialogInfo, getSalesAlertTrendInfo } from "@/api/index";
import { ref, computed, nextTick } from "vue";
import dayjs from "dayjs";

export function useSalesWarning() {
  // 状态管理
  const cycles = ref<string[]>(["07/01", "07/02", "07/03", "07/04", "07/05", "07/06", "07/07"]);
  const dates = ref<string[]>([]);
  const currentCycle = ref<string>(cycles.value[0]);
  const salesAlertData = ref<any[]>([]);
  const currentLob = ref<string>("");
  const chartsXAxisData = ref<string[]>([]);
  const chartsSeries = ref<{ name: string; data: number[] }[]>([]);

  // 根据选中日期过滤出当天的预警LOB数据
  const currentDateData = computed(() => {
    if (!salesAlertData.value.length || !currentCycle.value) return null;

    const selectedDate = salesAlertData.value.find((item) => {
      const formattedDate = dayjs(item.date).format("MM/DD");
      return formattedDate === currentCycle.value;
    });

    return selectedDate;
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    if (!currentDateData.value?.warning_lobs?.length) return [];

    const tabsData = currentDateData.value.warning_lobs.map((item) => ({
      label: item.lob,
      value: item.lob,
    }));

    // 自动选中第一个tab
    if (tabsData.length > 0 && !currentLob.value) {
      currentLob.value = tabsData[0].value;
    }

    return tabsData;
  });

  // 根据选中的LOB获取对应的levels数据
  const tableData = computed(() => {
    if (!currentDateData.value?.warning_lobs?.length || !currentLob.value) return [];

    const selectedLobData = currentDateData.value.warning_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData?.levels) return [];

    return selectedLobData.levels.map((level) => ({
      level: getLevelDisplayName(level.level),
      warning_times: level.warn_cnt.toString(),
      warning_store: level.pos_cnt.toString(),
    }));
  });

  // 表格列配置
  const columns = ref<{ label: string; prop: string }[]>([
    {
      label: "",
      prop: "level",
    },
    {
      label: "预警次数",
      prop: "warning_times",
    },
    {
      label: "预警门店",
      prop: "warning_store",
    },
  ]);

  const getLevelDisplayName = (level: string) => {
    const levelMap = {
      very_severe: "非常严重",
      severe: "严重",
      medium: "中等",
    };
    return levelMap[level] || level;
  };

  // 处理tab切换
  const handleTabChange = (lobValue: string) => {
    currentLob.value = lobValue;
  };

  // 加载销售预警对话框信息
  const loadSalesAlertDialogInfo = async () => {
    try {
      const res = await getSalesAlertDialogInfo();

      if (res.data && res.code === 0 && Array.isArray(res.data)) {
        salesAlertData.value = res.data;

        // 更新日期选择器的选项
        dates.value = res.data.map((item: any) => dayjs(item?.date).format("MM/DD"));

        // 设置默认选中第一个日期
        if (dates.value.length > 0) {
          nextTick(() => {
            currentCycle.value = dates.value[0];
            console.log("🚀 ~ nextTick ~ currentCycle.value :", currentCycle.value);
          });
        }

        console.log("🚀 ~ loadSalesAlertDialogInfo ~ salesAlertData:", salesAlertData.value);
        console.log("🚀 ~ loadSalesAlertDialogInfo ~ dates.value:", dates.value);
      }
    } catch (error) {
      console.log(error, 7867868768);
    }
  };

  // 加载销售预警趋势信息
  const loadSalesAlertTrendInfo = async () => {
    try {
      const res = await getSalesAlertTrendInfo();
      if (res.data && Array.isArray(res.data)) {
        // 提取 week_name 作为 x 轴数据
        chartsXAxisData.value = res.data.map((item: any) => item.week_name);

        // 构建系列数据
        const veryServerData = res.data.map((item: any) => item.very_severe || 0);
        const severeData = res.data.map((item: any) => item.severe || 0);
        const mediumData = res.data.map((item: any) => item.medium || 0);

        chartsSeries.value = [
          { name: "非常严重", data: veryServerData },
          { name: "严重", data: severeData },
          { name: "中等", data: mediumData },
        ];
      }
    } catch (error) {
      console.log(error, 7867868768);
    }
  };

  // 初始化数据
  const initData = () => {
    loadSalesAlertDialogInfo();
    loadSalesAlertTrendInfo();
  };

  return {
    // 状态
    cycles,
    dates,
    currentCycle,
    salesAlertData,
    currentLob,
    chartsXAxisData,
    chartsSeries,

    // 计算属性
    currentDateData,
    tabs,
    tableData,
    columns,

    // 方法
    handleTabChange,
    loadSalesAlertDialogInfo,
    loadSalesAlertTrendInfo,
    initData,
  };
}
