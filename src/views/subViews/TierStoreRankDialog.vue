<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo } from "@/api/index";
import { formatProductRank } from "@/intermediates/format/formatProductRankDialog";
import { PopoverRankItem } from "@dmp/components/src";

const { lob } = defineProps<{ lob: string }>();
const { t } = useI18n();

// tab
const tabOptions = ref([
  { label: "Tier1", value: "tier1" },
  { label: "Tier2", value: "tier2" },
  { label: "Tier3", value: "tier3" },
]);
const tabValue = ref(tabOptions.value[0].value);

// api
const rankData: Ref<Record<string, PopoverRankItem[]>> = ref({});
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ lob, start: 0, offset: 10 })
    .then(({ data = {} }) => {
      rankData.value = formatProductRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});

// computed
const currentRankData = computed(() => {
  const key = `last_week_${tabValue.value}`;
  return rankData.value?.[key] ?? [];
});
</script>

<template>
  <DmpTabSwitcher class="mt-[12px]" :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
  <DmpPopoverRank
    class="mt-[12px]"
    :name-title="t('排名')"
    :columns="['1fr']"
    :list="currentRankData"
    :loading="loading"
  />
</template>
