import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import svgLoader from "vite-svg-loader";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { DmpResolver } from "@dmp/resolver";
import eslint from "vite-plugin-eslint";

export default defineConfig(({ mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  const env = loadEnv(mode, process.cwd(), "");

  return {
    base: mode === 'development' ? '/' : '/mybizv2/',
    plugins: [
      vue(),
      eslint({
        include: ["src/**/*.js", "src/**/*.vue", "src/**/*.ts"],
        exclude: ["node_modules", "dist"],
        cache: false,
        emitWarning: true,
        emitError: true,
        failOnWarning: false,
        failOnError: false,
      }),
      svgLoader({
        defaultImport: "component", // 默认作为组件导入
      }),
      // 自动导入 Element Plus 和 Mybiz API
      AutoImport({
        resolvers: [ElementPlusResolver(), DmpResolver()],
        imports: ["vue", "vue-router", "pinia", "@vueuse/core"],
        ignore: ["h"],
        dirs: ["src/components/ui/PureNotify/utils"], // 导入 notify
        dts: true, // 生成类型声明文件
        eslintrc: {
          enabled: true, // 生成 eslint 配置
        },
      }),
      // 自动导入 Element Plus 和 Mybiz 组件
      Components({
        resolvers: [ElementPlusResolver(), DmpResolver()],
        dirs: [resolve(__dirname, "src/components/ui")], // 自动导入组件
        dts: true, // 生成类型声明文件
      }),
    ],
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
        "@assets": resolve(__dirname, "assets"),
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["vue", "vue-router", "pinia"],
            ui: ["element-plus"],
            utils: ["@vueuse/core"],
          },
        },
      },
    },
    server: {
      port: 3000,
      open: true,
      proxy: {
        // API 代理配置
        "/mybusiness": {
          target: env.VITE_API_TARGET || "https://gcdmp-eng.corp.apple.com/v2/",
          changeOrigin: true,
          secure: false,
        },
      },
    },
    // TypeScript support
    esbuild: {
      target: "es2020",
    },
  };
});
